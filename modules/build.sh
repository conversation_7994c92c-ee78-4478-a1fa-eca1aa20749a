#!/bin/bash

# Build Operations Module for DOCMS Task Runner
# Contains all build-related functions

# Source utilities
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# Category 1: Build Operations
build_menu() {
    local options=(
        "Docker Build & Push"
        "Maven Build"
        "NPM Build & Package"
        "Gradle Build"
        "Clean Build Artifacts"
        "Build Status Check"
    )
    
    local functions=(
        "docker_build_push"
        "maven_build"
        "npm_build"
        "gradle_build"
        "clean_artifacts"
        "build_status"
    )
    
    while true; do
        show_menu_options "BUILD OPERATIONS" "${options[@]}"
        read -r choice
        
        if ! handle_menu_choice "$choice" "${#options[@]}" "${functions[@]}"; then
            break
        fi
    done
}

docker_build_push() {
    info_message "Docker Build & Push"
    
    local image_name
    local tag
    
    image_name=$(read_input "Enter Docker image name")
    if ! validate_not_empty "$image_name" "Image name"; then
        press_enter
        return
    fi
    
    tag=$(read_input "Enter tag" "latest")
    
    if confirm_action; then
        execute_with_logging "docker build -t '$image_name:$tag' ." "Docker build for $image_name:$tag" && \
        execute_with_logging "docker push '$image_name:$tag'" "Docker push for $image_name:$tag"
    fi
    press_enter
}

maven_build() {
    info_message "Maven Build"
    
    local build_options=(
        "Clean compile"
        "Package"
        "Install"
        "Test"
    )
    
    local build_commands=(
        "clean compile"
        "clean package"
        "clean install"
        "test"
    )
    
    echo -e "${CYAN}Select build type:${NC}"
    for i in "${!build_options[@]}"; do
        echo "$((i+1)). ${build_options[$i]}"
    done
    
    read -r build_type
    
    if [[ "$build_type" =~ ^[1-4]$ ]]; then
        local mvn_cmd="${build_commands[$((build_type-1))]}"
        
        if confirm_action; then
            execute_with_logging "mvn $mvn_cmd" "Maven build: $mvn_cmd"
        fi
    else
        error_message "Invalid option"
    fi
    press_enter
}

npm_build() {
    info_message "NPM Build & Package"
    
    local npm_options=(
        "Install dependencies"
        "Build"
        "Test"
        "Full pipeline (install + build + test)"
    )
    
    local npm_commands=(
        "npm install"
        "npm run build"
        "npm test"
        "npm install && npm run build && npm test"
    )
    
    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!npm_options[@]}"; do
        echo "$((i+1)). ${npm_options[$i]}"
    done
    
    read -r npm_choice
    
    if [[ "$npm_choice" =~ ^[1-4]$ ]]; then
        local npm_cmd="${npm_commands[$((npm_choice-1))]}"
        
        if confirm_action; then
            execute_with_logging "$npm_cmd" "NPM operation: ${npm_options[$((npm_choice-1))]}"
        fi
    else
        error_message "Invalid option"
    fi
    press_enter
}

gradle_build() {
    info_message "Gradle Build"
    
    local gradle_options=(
        "Clean"
        "Build"
        "Test"
        "Assemble"
    )
    
    local gradle_commands=(
        "clean"
        "build"
        "test"
        "assemble"
    )
    
    echo -e "${CYAN}Select build task:${NC}"
    for i in "${!gradle_options[@]}"; do
        echo "$((i+1)). ${gradle_options[$i]}"
    done
    
    read -r gradle_choice
    
    if [[ "$gradle_choice" =~ ^[1-4]$ ]]; then
        local gradle_cmd="${gradle_commands[$((gradle_choice-1))]}"
        
        if confirm_action; then
            execute_with_logging "./gradlew $gradle_cmd" "Gradle task: $gradle_cmd"
        fi
    else
        error_message "Invalid option"
    fi
    press_enter
}

clean_artifacts() {
    info_message "Clean Build Artifacts"
    warning_message "This will remove:"
    echo "- target/ directories"
    echo "- build/ directories" 
    echo "- node_modules/ directories"
    echo "- *.log files"
    
    if confirm_action; then
        log "Cleaning build artifacts"
        
        local cleanup_commands=(
            "find . -name 'target' -type d -exec rm -rf {} + 2>/dev/null || true"
            "find . -name 'build' -type d -exec rm -rf {} + 2>/dev/null || true"
            "find . -name 'node_modules' -type d -exec rm -rf {} + 2>/dev/null || true"
            "find . -name '*.log' -type f -delete 2>/dev/null || true"
        )
        
        for cmd in "${cleanup_commands[@]}"; do
            eval "$cmd"
        done
        
        success_message "Build artifacts cleaned"
    fi
    press_enter
}

build_status() {
    info_message "Build Status Check"
    info_message "Checking build environment..."
    
    local tools=(
        "docker:Docker"
        "mvn:Maven"
        "node:Node.js"
        "npm:NPM"
        "gradle:Gradle"
        "java:Java"
        "python3:Python"
        "go:Go"
        "cargo:Rust"
    )
    
    for tool_info in "${tools[@]}"; do
        IFS=':' read -r tool description <<< "$tool_info"
        echo -e "${CYAN}$description:${NC}"
        
        if command -v "$tool" &> /dev/null; then
            success_message "Available"
            case "$tool" in
                "docker") docker --version 2>/dev/null | head -1 ;;
                "mvn") mvn --version 2>/dev/null | head -1 ;;
                "node") node --version 2>/dev/null ;;
                "npm") npm --version 2>/dev/null ;;
                "gradle") gradle --version 2>/dev/null | head -1 ;;
                "java") java --version 2>/dev/null | head -1 ;;
                "python3") python3 --version 2>/dev/null ;;
                "go") go version 2>/dev/null ;;
                "cargo") cargo --version 2>/dev/null ;;
            esac
        else
            error_message "Not available"
        fi
        echo
    done
    
    press_enter
}

# Export functions
export -f build_menu docker_build_push maven_build npm_build gradle_build clean_artifacts build_status
