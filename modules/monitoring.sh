#!/bin/bash

# Monitoring Operations Module for DOCMS Task Runner
# Contains all monitoring-related functions

# Source utilities
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# Category 4: Monitoring Operations
monitoring_menu() {
    local options=(
        "System Health Check"
        "Docker Container Monitoring"
        "Log Analysis"
        "Network Monitoring"
        "Disk Usage Check"
        "Process Monitoring"
        "Service Status Check"
        "Performance Metrics"
    )
    
    local functions=(
        "system_health_check"
        "docker_monitoring"
        "log_analysis"
        "network_monitoring"
        "disk_usage_check"
        "process_monitoring"
        "service_status_check"
        "performance_metrics"
    )
    
    while true; do
        show_menu_options "MONITORING OPERATIONS" "${options[@]}"
        read -r choice
        
        if ! handle_menu_choice "$choice" "${#options[@]}" "${functions[@]}"; then
            break
        fi
    done
}

system_health_check() {
    info_message "System Health Check"
    log "Running system health check"
    
    info_message "System Information:"
    echo -e "${CYAN}Hostname:${NC} $(hostname)"
    echo -e "${CYAN}Uptime:${NC} $(uptime)"
    echo -e "${CYAN}Load Average:${NC} $(uptime | awk -F'load average:' '{print $2}')"
    
    echo
    info_message "Memory Usage:"
    if command -v free &> /dev/null; then
        free -h
    else
        echo "Memory info not available on this system"
    fi
    
    echo
    info_message "CPU Usage:"
    if command -v top &> /dev/null; then
        top -bn1 | grep "Cpu(s)" | awk '{print $2 $3 $4 $5 $6 $7 $8}' 2>/dev/null || echo "CPU info not available"
    fi
    
    echo
    info_message "Disk Usage:"
    df -h | head -10
    
    echo
    info_message "Network Interfaces:"
    if command -v ip &> /dev/null; then
        ip addr show | grep -E '^[0-9]+:|inet ' | head -10
    else
        ifconfig 2>/dev/null | head -20 || echo "Network info not available"
    fi
    
    press_enter
}

docker_monitoring() {
    info_message "Docker Container Monitoring"
    
    if ! check_tool_available "docker" "Docker"; then
        press_enter
        return
    fi
    
    if ! docker info &>/dev/null; then
        error_message "Docker daemon is not running"
        press_enter
        return
    fi
    
    success_message "Docker daemon is running"
    
    echo
    info_message "Running Containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Image}}" 2>/dev/null || error_message "Failed to list containers"
    
    echo
    info_message "Container Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" 2>/dev/null || error_message "Failed to get container stats"
    
    echo
    info_message "Docker System Info:"
    docker system df 2>/dev/null || error_message "Failed to get system info"
    
    echo
    echo -e "${CYAN}Monitor specific container? (y/N): ${NC}"
    read -r monitor_specific
    
    if [[ "$monitor_specific" =~ ^[yY]$ ]]; then
        local container_name
        container_name=$(read_input "Enter container name")
        if validate_not_empty "$container_name" "Container name"; then
            info_message "Real-time stats for $container_name (Press Ctrl+C to stop):"
            docker stats "$container_name"
        fi
    fi
    
    press_enter
}

log_analysis() {
    info_message "Log Analysis"
    
    local log_sources=(
        "System logs (journalctl)"
        "Application logs"
        "Docker container logs"
        "Custom log file"
    )
    
    echo -e "${CYAN}Select log source:${NC}"
    for i in "${!log_sources[@]}"; do
        echo "$((i+1)). ${log_sources[$i]}"
    done
    
    read -r log_choice
    
    case $log_choice in
        1) _analyze_system_logs ;;
        2) _analyze_application_logs ;;
        3) _analyze_docker_logs ;;
        4) _analyze_custom_logs ;;
        *) error_message "Invalid option" ;;
    esac
    
    press_enter
}

_analyze_system_logs() {
    if command -v journalctl &> /dev/null; then
        local time_options=(
            "Last hour"
            "Last 24 hours"
            "Last week"
            "Custom"
        )
        
        echo -e "${CYAN}Select time range:${NC}"
        for i in "${!time_options[@]}"; do
            echo "$((i+1)). ${time_options[$i]}"
        done
        
        read -r time_choice
        
        case $time_choice in
            1) journalctl --since "1 hour ago" ;;
            2) journalctl --since "24 hours ago" ;;
            3) journalctl --since "1 week ago" ;;
            4) 
                local custom_time
                custom_time=$(read_input "Enter time range (e.g., '2023-01-01 00:00:00')")
                if validate_not_empty "$custom_time" "Time range"; then
                    journalctl --since "$custom_time"
                fi
                ;;
            *) error_message "Invalid option" ;;
        esac
    else
        error_message "journalctl not available"
    fi
}

_analyze_application_logs() {
    local log_dir
    log_dir=$(read_input "Enter application log directory")
    
    if validate_directory_exists "$log_dir" "Log directory"; then
        info_message "Recent log files in $log_dir:"
        find "$log_dir" -name "*.log" -mtime -1 -exec ls -la {} \; 2>/dev/null || echo "No recent log files found"
        
        local log_file
        log_file=$(read_input "Enter specific log file to analyze")
        if validate_file_exists "$log_file" "Log file"; then
            tail -100 "$log_file"
        fi
    fi
}

_analyze_docker_logs() {
    local container_name
    local lines
    
    container_name=$(read_input "Enter container name")
    if ! validate_not_empty "$container_name" "Container name"; then
        return
    fi
    
    lines=$(read_input "Number of lines to show" "100")
    docker logs --tail "$lines" "$container_name" 2>/dev/null || error_message "Failed to get container logs"
}

_analyze_custom_logs() {
    local log_file
    log_file=$(read_input "Enter log file path")
    
    if validate_file_exists "$log_file" "Log file"; then
        local analysis_options=(
            "Show last 100 lines"
            "Search for pattern"
            "Show error lines"
            "Show file statistics"
        )
        
        echo -e "${CYAN}Analysis options:${NC}"
        for i in "${!analysis_options[@]}"; do
            echo "$((i+1)). ${analysis_options[$i]}"
        done
        
        read -r analysis_choice
        
        case $analysis_choice in
            1) tail -100 "$log_file" ;;
            2) 
                local pattern
                pattern=$(read_input "Enter search pattern")
                if validate_not_empty "$pattern" "Search pattern"; then
                    grep -n "$pattern" "$log_file" || echo "No matches found"
                fi
                ;;
            3) grep -i "error\|exception\|fail" "$log_file" || echo "No errors found" ;;
            4) 
                info_message "File: $log_file"
                echo -e "${CYAN}Size:${NC} $(du -h "$log_file" | cut -f1)"
                echo -e "${CYAN}Lines:${NC} $(wc -l < "$log_file")"
                echo -e "${CYAN}Last modified:${NC} $(stat -c %y "$log_file" 2>/dev/null || stat -f %Sm "$log_file" 2>/dev/null || echo "Unknown")"
                ;;
            *) error_message "Invalid option" ;;
        esac
    fi
}

network_monitoring() {
    info_message "Network Monitoring"
    
    info_message "Network Interfaces:"
    if command -v ip &> /dev/null; then
        ip addr show
    else
        ifconfig 2>/dev/null || echo "Network interface info not available"
    fi
    
    echo
    info_message "Network Statistics:"
    if command -v netstat &> /dev/null; then
        netstat -i 2>/dev/null || echo "Network statistics not available"
    fi
    
    echo
    info_message "Active Connections:"
    if command -v netstat &> /dev/null; then
        netstat -tuln 2>/dev/null | head -20 || echo "Connection info not available"
    fi
    
    echo
    echo -e "${CYAN}Test connectivity to specific host? (y/N): ${NC}"
    read -r test_connectivity_choice
    
    if [[ "$test_connectivity_choice" =~ ^[yY]$ ]]; then
        local target_host
        local target_port
        
        target_host=$(read_input "Enter hostname or IP")
        if validate_not_empty "$target_host" "Hostname"; then
            target_port=$(read_input "Enter port (optional)")
            
            info_message "Ping test:"
            ping -c 4 "$target_host" 2>/dev/null || error_message "Ping failed"
            
            if [[ -n "$target_port" ]]; then
                info_message "Port connectivity test:"
                if command -v nc &> /dev/null; then
                    nc -zv "$target_host" "$target_port" 2>&1 || error_message "Port test failed"
                else
                    warning_message "netcat not available for port testing"
                fi
            fi
        fi
    fi
    
    press_enter
}

disk_usage_check() {
    info_message "Disk Usage Check"
    
    info_message "Filesystem Usage:"
    df -h
    
    echo
    info_message "Inode Usage:"
    df -i 2>/dev/null || echo "Inode information not available"
    
    echo
    info_message "Largest Directories (Top 10):"
    du -h / 2>/dev/null | sort -rh | head -10 || echo "Directory analysis not available"
    
    echo
    echo -e "${CYAN}Check specific directory? (y/N): ${NC}"
    read -r check_specific
    
    if [[ "$check_specific" =~ ^[yY]$ ]]; then
        local dir_path
        dir_path=$(read_input "Enter directory path")
        if validate_directory_exists "$dir_path" "Directory"; then
            info_message "Directory usage for $dir_path:"
            du -h "$dir_path" 2>/dev/null | sort -rh | head -20 || error_message "Failed to analyze directory"
        fi
    fi
    
    press_enter
}

process_monitoring() {
    info_message "Process Monitoring"
    
    info_message "Top Processes by CPU:"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        ps -Ao pid,ppid,comm,%cpu,%mem,etime | head -10
    else
        ps aux --sort=-%cpu 2>/dev/null | head -10 || ps aux | head -10
    fi
    
    echo
    info_message "Top Processes by Memory:"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        ps -Ao pid,ppid,comm,%cpu,%mem,etime | sort -k5 -nr | head -10
    else
        ps aux --sort=-%mem 2>/dev/null | head -10 || ps aux | head -10
    fi
    
    echo
    info_message "Process Tree:"
    if command -v pstree &> /dev/null; then
        pstree -p | head -20
    else
        echo "pstree not available"
    fi
    
    echo
    echo -e "${CYAN}Monitor specific process? (y/N): ${NC}"
    read -r monitor_process
    
    if [[ "$monitor_process" =~ ^[yY]$ ]]; then
        local process_name
        process_name=$(read_input "Enter process name or PID")
        
        if validate_not_empty "$process_name" "Process name/PID"; then
            if [[ "$process_name" =~ ^[0-9]+$ ]]; then
                # It's a PID
                ps -p "$process_name" -o pid,ppid,cmd,%cpu,%mem,etime 2>/dev/null || error_message "Process not found"
            else
                # It's a process name
                if command -v pgrep &> /dev/null; then
                    pgrep -f "$process_name" | xargs -I {} ps -p {} -o pid,ppid,cmd,%cpu,%mem,etime 2>/dev/null || error_message "Process not found"
                else
                    ps aux | grep "$process_name" | grep -v grep || error_message "Process not found"
                fi
            fi
        fi
    fi
    
    press_enter
}

service_status_check() {
    info_message "Service Status Check"
    
    if command -v systemctl &> /dev/null; then
        info_message "Systemd Services Status:"
        systemctl list-units --type=service --state=running | head -20
        
        echo
        info_message "Failed Services:"
        systemctl list-units --type=service --state=failed
        
        echo
        echo -e "${CYAN}Check specific service? (y/N): ${NC}"
        read -r check_service
        
        if [[ "$check_service" =~ ^[yY]$ ]]; then
            local service_name
            service_name=$(read_input "Enter service name")
            
            if validate_not_empty "$service_name" "Service name"; then
                info_message "Service Status:"
                systemctl status "$service_name"
                
                echo
                echo -e "${CYAN}Show service logs? (y/N): ${NC}"
                read -r show_logs
                
                if [[ "$show_logs" =~ ^[yY]$ ]]; then
                    journalctl -u "$service_name" --since "1 hour ago"
                fi
            fi
        fi
    else
        warning_message "systemctl not available. Checking basic service status:"
        if command -v service &> /dev/null; then
            service --status-all 2>/dev/null | head -20 || echo "Service status not available"
        else
            echo "No service management tools available"
        fi
    fi
    
    press_enter
}

performance_metrics() {
    info_message "Performance Metrics"
    
    info_message "System Load:"
    uptime
    
    echo
    info_message "CPU Information:"
    if command -v lscpu &> /dev/null; then
        lscpu | grep -E "Model name|CPU\(s\)|Thread|Core"
    else
        echo "CPU info not available"
    fi
    
    echo
    info_message "Memory Information:"
    if command -v free &> /dev/null; then
        free -h
        cat /proc/meminfo 2>/dev/null | grep -E "MemTotal|MemFree|MemAvailable|Cached|Buffers" || echo "Detailed memory info not available"
    else
        echo "Memory info not available"
    fi
    
    echo
    info_message "I/O Statistics:"
    if command -v iostat &> /dev/null; then
        iostat -x 1 1
    else
        warning_message "iostat not available. Install sysstat package for detailed I/O stats."
        if [[ -f "/proc/diskstats" ]]; then
            cat /proc/diskstats | head -5
        fi
    fi
    
    echo
    echo -e "${CYAN}Run continuous monitoring? (y/N): ${NC}"
    read -r continuous_monitor
    
    if [[ "$continuous_monitor" =~ ^[yY]$ ]]; then
        info_message "Starting continuous monitoring (Press Ctrl+C to stop):"
        if command -v htop &> /dev/null; then
            htop
        else
            top
        fi
    fi
    
    press_enter
}

# Export functions
export -f monitoring_menu system_health_check docker_monitoring log_analysis network_monitoring
export -f disk_usage_check process_monitoring service_status_check performance_metrics
