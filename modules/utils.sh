#!/bin/bash

# Core Utilities Module for DOCMS Task Runner
# Contains shared functions, colors, logging, and common utilities

# Colors for better UX
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_FILE="${SCRIPT_DIR}/task-runner.log"

# Utility functions
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

print_header() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                    DOCMS Task Runner                         ║${NC}"
    echo -e "${BLUE}║              Interactive DevOps Task Manager                 ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

print_category_header() {
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                         $1${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

confirm_action() {
    echo -e "${YELLOW}Are you sure you want to proceed? (y/N): ${NC}"
    read -r confirmation
    case $confirmation in
        [yY]|[yY][eE][sS]) return 0 ;;
        *) echo -e "${RED}Operation cancelled.${NC}"; return 1 ;;
    esac
}

press_enter() {
    echo -e "${CYAN}Press Enter to continue...${NC}"
    read -r
}

# Success/Error message functions
success_message() {
    echo -e "${GREEN}✓ $1${NC}"
}

error_message() {
    echo -e "${RED}✗ $1${NC}"
}

warning_message() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

info_message() {
    echo -e "${BLUE}$1${NC}"
}

# Input validation functions
validate_not_empty() {
    local input="$1"
    local field_name="$2"
    
    if [[ -z "$input" ]]; then
        error_message "$field_name cannot be empty"
        return 1
    fi
    return 0
}

validate_file_exists() {
    local file_path="$1"
    local description="$2"
    
    if [[ ! -f "$file_path" ]]; then
        error_message "$description not found: $file_path"
        return 1
    fi
    return 0
}

validate_directory_exists() {
    local dir_path="$1"
    local description="$2"
    
    if [[ ! -d "$dir_path" ]]; then
        error_message "$description not found: $dir_path"
        return 1
    fi
    return 0
}

# Tool availability check
check_tool_available() {
    local tool="$1"
    local description="$2"
    
    if command -v "$tool" &> /dev/null; then
        success_message "$description is available"
        return 0
    else
        error_message "$description is not available"
        return 1
    fi
}

# Network connectivity test
test_connectivity() {
    local host="$1"
    local description="$2"
    local timeout="${3:-3}"
    
    echo -n -e "${CYAN}Testing $description ($host)... ${NC}"
    if ping -c 1 -W "$timeout" "$host" &>/dev/null; then
        success_message "Reachable"
        return 0
    else
        error_message "Unreachable"
        return 1
    fi
}

# Execute command with logging
execute_with_logging() {
    local command="$1"
    local description="$2"
    
    log "Executing: $description - Command: $command"
    
    if eval "$command"; then
        success_message "$description completed successfully"
        log "Success: $description"
        return 0
    else
        error_message "$description failed"
        log "Failed: $description"
        return 1
    fi
}

# Read input with prompt
read_input() {
    local prompt="$1"
    local default_value="$2"
    local variable_name="$3"
    
    if [[ -n "$default_value" ]]; then
        echo -e "${CYAN}$prompt (default: $default_value): ${NC}"
    else
        echo -e "${CYAN}$prompt: ${NC}"
    fi
    
    read -r input_value
    
    if [[ -z "$input_value" && -n "$default_value" ]]; then
        input_value="$default_value"
    fi
    
    if [[ -n "$variable_name" ]]; then
        eval "$variable_name='$input_value'"
    fi
    
    echo "$input_value"
}

# Read password securely
read_password() {
    local prompt="$1"
    local variable_name="$2"
    
    echo -e "${CYAN}$prompt: ${NC}"
    read -s password_value
    echo
    
    if [[ -n "$variable_name" ]]; then
        eval "$variable_name='$password_value'"
    fi
    
    echo "$password_value"
}

# Show menu options
show_menu_options() {
    local title="$1"
    shift
    local options=("$@")
    
    print_category_header "$title"
    echo -e "${GREEN}Available options:${NC}"
    echo ""
    
    local i=1
    for option in "${options[@]}"; do
        echo -e "${YELLOW}$i.${NC} $option"
        ((i++))
    done
    echo -e "${YELLOW}0.${NC} Back to Main Menu"
    echo ""
    echo -e "${CYAN}Enter your choice [0-$((${#options[@]})]: ${NC}"
}

# Generic menu handler
handle_menu_choice() {
    local choice="$1"
    local max_options="$2"
    shift 2
    local functions=("$@")
    
    if [[ "$choice" == "0" ]]; then
        return 1  # Signal to go back
    elif [[ "$choice" =~ ^[1-9][0-9]*$ ]] && [[ "$choice" -le "$max_options" ]]; then
        local func_index=$((choice - 1))
        if [[ -n "${functions[$func_index]}" ]]; then
            "${functions[$func_index]}"
            return 0
        fi
    fi
    
    error_message "Invalid option. Please try again."
    press_enter
    return 0
}

# Load configuration if available
load_config() {
    local config_file="${SCRIPT_DIR}/config.sh"
    if [[ -f "$config_file" ]]; then
        source "$config_file"
        log "Configuration loaded from $config_file"
    fi
}

# Initialize utilities
init_utils() {
    # Create log file if it doesn't exist
    touch "$LOG_FILE"
    log "Task Runner utilities initialized"
    
    # Load configuration
    load_config
}

# Export functions for use in other modules
export -f log print_header print_category_header confirm_action press_enter
export -f success_message error_message warning_message info_message
export -f validate_not_empty validate_file_exists validate_directory_exists
export -f check_tool_available test_connectivity execute_with_logging
export -f read_input read_password show_menu_options handle_menu_choice
export -f load_config init_utils

# Export variables
export RED GREEN YELLOW BLUE PURPLE CYAN NC
export SCRIPT_DIR LOG_FILE
