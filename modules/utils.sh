#!/bin/bash

# Core Utilities Module for DOCMS Task Runner

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_FILE="${SCRIPT_DIR}/task-runner.log"

# Basic functions
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

success_message() {
    echo -e "${GREEN}✓ $1${NC}"
}

error_message() {
    echo -e "${RED}✗ $1${NC}"
}

info_message() {
    echo -e "${BLUE}$1${NC}"
}

warning_message() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_header() {
    clear
    echo -e "${BLUE}+==============================================================+${NC}"
    echo -e "${BLUE}|                    DOCMS Task Runner                         |${NC}"
    echo -e "${BLUE}|              Interactive DevOps Task Manager                 |${NC}"
    echo -e "${BLUE}+==============================================================+${NC}"
    echo ""
}

print_category_header() {
    echo -e "${CYAN}+==============================================================+${NC}"
    echo -e "${CYAN}|                         $1${NC}"
    echo -e "${CYAN}+==============================================================+${NC}"
    echo ""
}

press_enter() {
    echo -e "${CYAN}Press Enter to continue...${NC}"
    read -r
}

confirm_action() {
    echo -e "${YELLOW}Are you sure you want to proceed? (y/N): ${NC}"
    read -r confirmation
    case $confirmation in
        [yY]|[yY][eE][sS]) return 0 ;;
        *) echo -e "${RED}Operation cancelled.${NC}"; return 1 ;;
    esac
}

validate_not_empty() {
    local input="$1"
    local field_name="$2"

    if [[ -z "$input" ]]; then
        error_message "$field_name cannot be empty"
        return 1
    fi
    return 0
}

validate_file_exists() {
    local file_path="$1"
    local description="$2"

    if [[ ! -f "$file_path" ]]; then
        error_message "$description not found: $file_path"
        return 1
    fi
    return 0
}

validate_directory_exists() {
    local dir_path="$1"
    local description="$2"

    if [[ ! -d "$dir_path" ]]; then
        error_message "$description not found: $dir_path"
        return 1
    fi
    return 0
}

check_tool_available() {
    local tool="$1"
    local description="$2"

    if command -v "$tool" &> /dev/null; then
        success_message "$description is available"
        return 0
    else
        error_message "$description is not available"
        return 1
    fi
}

execute_with_logging() {
    local command="$1"
    local description="$2"

    log "Executing: $description - Command: $command"

    if eval "$command"; then
        success_message "$description completed successfully"
        log "Success: $description"
        return 0
    else
        error_message "$description failed"
        log "Failed: $description"
        return 1
    fi
}

read_input() {
    local prompt="$1"
    local default_value="$2"
    local variable_name="$3"

    if [[ -n "$default_value" ]]; then
        echo -e "${CYAN}$prompt (default: $default_value): ${NC}"
    else
        echo -e "${CYAN}$prompt: ${NC}"
    fi

    read -r input_value

    if [[ -z "$input_value" && -n "$default_value" ]]; then
        input_value="$default_value"
    fi

    if [[ -n "$variable_name" ]]; then
        eval "$variable_name='$input_value'"
    fi

    echo "$input_value"
}

read_password() {
    local prompt="$1"
    local variable_name="$2"

    echo -e "${CYAN}$prompt: ${NC}"
    read -s password_value
    echo

    if [[ -n "$variable_name" ]]; then
        eval "$variable_name='$password_value'"
    fi

    echo "$password_value"
}

show_menu_options() {
    local title="$1"
    shift
    local options=("$@")

    print_category_header "$title"
    echo -e "${GREEN}Available options:${NC}"
    echo ""

    local i=1
    for option in "${options[@]}"; do
        echo -e "${YELLOW}$i.${NC} $option"
        ((i++))
    done
    echo -e "${YELLOW}0.${NC} Back to Main Menu"
    echo ""
    echo -e "${CYAN}Enter your choice [0-$((${#options[@]})]: ${NC}"
}

handle_menu_choice() {
    local choice="$1"
    local max_options="$2"
    shift 2
    local functions=("$@")

    if [[ "$choice" == "0" ]]; then
        return 1  # Signal to go back
    elif [[ "$choice" =~ ^[1-9][0-9]*$ ]] && [[ "$choice" -le "$max_options" ]]; then
        local func_index=$((choice - 1))
        if [[ -n "${functions[$func_index]}" ]]; then
            "${functions[$func_index]}"
            return 0
        fi
    fi

    error_message "Invalid option. Please try again."
    press_enter
    return 0
}

load_config() {
    local config_file="${SCRIPT_DIR}/config.sh"
    if [[ -f "$config_file" ]]; then
        source "$config_file"
        log "Configuration loaded from $config_file"
    fi
}

init_utils() {
    touch "$LOG_FILE"
    log "Task Runner utilities initialized"
    load_config
}

# Export all functions
export -f log success_message error_message info_message warning_message
export -f print_header print_category_header press_enter confirm_action
export -f validate_not_empty validate_file_exists validate_directory_exists
export -f check_tool_available execute_with_logging read_input read_password
export -f show_menu_options handle_menu_choice load_config init_utils
export RED GREEN YELLOW BLUE CYAN NC SCRIPT_DIR LOG_FILE
