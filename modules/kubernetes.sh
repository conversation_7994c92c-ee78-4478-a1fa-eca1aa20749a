#!/bin/bash

# Kubernetes Operations Module for DOCMS Task Runner
# Contains all Kubernetes-related functions

# Source utilities
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# Category 2: Kubernetes Operations
kubernetes_menu() {
    local options=(
        "Get Cluster Info"
        "Deploy Application"
        "Scale Deployment"
        "View Pods Status"
        "View Logs"
        "Port Forward"
        "Apply Manifests"
        "Delete Resources"
    )
    
    local functions=(
        "k8s_cluster_info"
        "k8s_deploy"
        "k8s_scale"
        "k8s_pods_status"
        "k8s_logs"
        "k8s_port_forward"
        "k8s_apply_manifests"
        "k8s_delete_resources"
    )
    
    while true; do
        show_menu_options "KUBERNETES OPERATIONS" "${options[@]}"
        read -r choice
        
        if ! handle_menu_choice "$choice" "${#options[@]}" "${functions[@]}"; then
            break
        fi
    done
}

k8s_cluster_info() {
    info_message "Kubernetes Cluster Information"
    log "Fetching Kubernetes cluster info"
    
    if ! check_tool_available "kubectl" "kubectl"; then
        press_enter
        return
    fi
    
    info_message "Cluster Info:"
    kubectl cluster-info 2>/dev/null || error_message "Unable to connect to cluster"
    
    echo
    info_message "Nodes:"
    kubectl get nodes 2>/dev/null || error_message "Unable to fetch nodes"
    
    echo
    info_message "Namespaces:"
    kubectl get namespaces 2>/dev/null || error_message "Unable to fetch namespaces"
    
    press_enter
}

k8s_deploy() {
    info_message "Deploy Application"
    
    local deployment_name
    local image_name
    local namespace
    
    deployment_name=$(read_input "Enter deployment name")
    if ! validate_not_empty "$deployment_name" "Deployment name"; then
        press_enter
        return
    fi
    
    image_name=$(read_input "Enter image name")
    if ! validate_not_empty "$image_name" "Image name"; then
        press_enter
        return
    fi
    
    namespace=$(read_input "Enter namespace" "default")
    
    if confirm_action; then
        execute_with_logging "kubectl create deployment '$deployment_name' --image='$image_name' -n '$namespace'" \
                            "Deploying $deployment_name with image $image_name to namespace $namespace"
    fi
    press_enter
}

k8s_scale() {
    info_message "Scale Deployment"
    
    local deployment_name
    local namespace
    local replicas
    
    deployment_name=$(read_input "Enter deployment name")
    if ! validate_not_empty "$deployment_name" "Deployment name"; then
        press_enter
        return
    fi
    
    namespace=$(read_input "Enter namespace" "default")
    
    replicas=$(read_input "Enter replica count")
    if ! validate_not_empty "$replicas" "Replica count"; then
        press_enter
        return
    fi
    
    if ! [[ "$replicas" =~ ^[0-9]+$ ]]; then
        error_message "Replica count must be a number"
        press_enter
        return
    fi
    
    if confirm_action; then
        execute_with_logging "kubectl scale deployment '$deployment_name' --replicas='$replicas' -n '$namespace'" \
                            "Scaling deployment $deployment_name to $replicas replicas"
    fi
    press_enter
}

k8s_pods_status() {
    info_message "Pods Status"
    
    local namespace
    namespace=$(read_input "Enter namespace (leave empty for all)")
    
    if [[ -z "$namespace" || "$namespace" == "all" ]]; then
        kubectl get pods --all-namespaces
    else
        kubectl get pods -n "$namespace"
    fi
    press_enter
}

k8s_logs() {
    info_message "View Pod Logs"
    
    local pod_name
    local namespace
    local follow
    
    pod_name=$(read_input "Enter pod name")
    if ! validate_not_empty "$pod_name" "Pod name"; then
        press_enter
        return
    fi
    
    namespace=$(read_input "Enter namespace" "default")
    
    echo -e "${CYAN}Follow logs? (y/N): ${NC}"
    read -r follow
    
    if [[ "$follow" =~ ^[yY]$ ]]; then
        kubectl logs -f "$pod_name" -n "$namespace"
    else
        kubectl logs "$pod_name" -n "$namespace"
    fi
    press_enter
}

k8s_port_forward() {
    info_message "Port Forward"
    
    local resource_name
    local namespace
    local local_port
    local remote_port
    
    resource_name=$(read_input "Enter pod/service name")
    if ! validate_not_empty "$resource_name" "Resource name"; then
        press_enter
        return
    fi
    
    namespace=$(read_input "Enter namespace" "default")
    
    local_port=$(read_input "Enter local port")
    if ! validate_not_empty "$local_port" "Local port"; then
        press_enter
        return
    fi
    
    remote_port=$(read_input "Enter remote port")
    if ! validate_not_empty "$remote_port" "Remote port"; then
        press_enter
        return
    fi
    
    info_message "Starting port forward... Press Ctrl+C to stop"
    kubectl port-forward "$resource_name" "$local_port:$remote_port" -n "$namespace"
    press_enter
}

k8s_apply_manifests() {
    info_message "Apply Kubernetes Manifests"
    
    local manifest_path
    manifest_path=$(read_input "Enter manifest file/directory path")
    
    if ! validate_file_exists "$manifest_path" "Manifest path" && ! validate_directory_exists "$manifest_path" "Manifest path"; then
        press_enter
        return
    fi
    
    if confirm_action; then
        execute_with_logging "kubectl apply -f '$manifest_path'" "Applying manifests from $manifest_path"
    fi
    press_enter
}

k8s_delete_resources() {
    info_message "Delete Kubernetes Resources"
    warning_message "WARNING: This will permanently delete resources"
    
    local resource_type
    local resource_name
    local namespace
    
    resource_type=$(read_input "Enter resource type (pod, deployment, service, etc.)")
    if ! validate_not_empty "$resource_type" "Resource type"; then
        press_enter
        return
    fi
    
    resource_name=$(read_input "Enter resource name")
    if ! validate_not_empty "$resource_name" "Resource name"; then
        press_enter
        return
    fi
    
    namespace=$(read_input "Enter namespace" "default")
    
    if confirm_action; then
        execute_with_logging "kubectl delete '$resource_type' '$resource_name' -n '$namespace'" \
                            "Deleting $resource_type/$resource_name from namespace $namespace"
    fi
    press_enter
}

# Export functions
export -f kubernetes_menu k8s_cluster_info k8s_deploy k8s_scale k8s_pods_status k8s_logs k8s_port_forward k8s_apply_manifests k8s_delete_resources
