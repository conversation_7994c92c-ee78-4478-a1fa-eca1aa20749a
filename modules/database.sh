#!/bin/bash

# Database Operations Module for DOCMS Task Runner
# Contains all database-related functions

# Source utilities
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# Category 3: Database Operations
database_menu() {
    local options=(
        "PostgreSQL Operations"
        "MySQL Operations"
        "MongoDB Operations"
        "Database Backup"
        "Database Restore"
        "Connection Test"
        "Schema Migration"
    )
    
    local functions=(
        "postgresql_operations"
        "mysql_operations"
        "mongodb_operations"
        "database_backup"
        "database_restore"
        "database_connection_test"
        "schema_migration"
    )
    
    while true; do
        show_menu_options "DATABASE OPERATIONS" "${options[@]}"
        read -r choice
        
        if ! handle_menu_choice "$choice" "${#options[@]}" "${functions[@]}"; then
            break
        fi
    done
}

postgresql_operations() {
    info_message "PostgreSQL Operations"
    
    local pg_host
    local pg_database
    local pg_user
    
    pg_host=$(read_input "Enter PostgreSQL host")
    if ! validate_not_empty "$pg_host" "PostgreSQL host"; then
        press_enter
        return
    fi
    
    pg_database=$(read_input "Enter database name")
    if ! validate_not_empty "$pg_database" "Database name"; then
        press_enter
        return
    fi
    
    pg_user=$(read_input "Enter username")
    if ! validate_not_empty "$pg_user" "Username"; then
        press_enter
        return
    fi
    
    local pg_options=(
        "Connect and list tables"
        "Execute custom query"
        "Show database size"
        "Show active connections"
    )
    
    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!pg_options[@]}"; do
        echo "$((i+1)). ${pg_options[$i]}"
    done
    
    read -r pg_choice
    
    case $pg_choice in
        1) 
            psql -h "$pg_host" -d "$pg_database" -U "$pg_user" -c "\dt"
            ;;
        2)
            local sql_query
            sql_query=$(read_input "Enter SQL query")
            if validate_not_empty "$sql_query" "SQL query"; then
                psql -h "$pg_host" -d "$pg_database" -U "$pg_user" -c "$sql_query"
            fi
            ;;
        3)
            psql -h "$pg_host" -d "$pg_database" -U "$pg_user" -c "SELECT pg_size_pretty(pg_database_size('$pg_database'));"
            ;;
        4)
            psql -h "$pg_host" -d "$pg_database" -U "$pg_user" -c "SELECT * FROM pg_stat_activity;"
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

mysql_operations() {
    info_message "MySQL Operations"
    
    local mysql_host
    local mysql_database
    local mysql_user
    
    mysql_host=$(read_input "Enter MySQL host")
    if ! validate_not_empty "$mysql_host" "MySQL host"; then
        press_enter
        return
    fi
    
    mysql_database=$(read_input "Enter database name")
    if ! validate_not_empty "$mysql_database" "Database name"; then
        press_enter
        return
    fi
    
    mysql_user=$(read_input "Enter username")
    if ! validate_not_empty "$mysql_user" "Username"; then
        press_enter
        return
    fi
    
    local mysql_options=(
        "Show tables"
        "Execute custom query"
        "Show database size"
        "Show processlist"
    )
    
    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!mysql_options[@]}"; do
        echo "$((i+1)). ${mysql_options[$i]}"
    done
    
    read -r mysql_choice
    
    case $mysql_choice in
        1) 
            mysql -h "$mysql_host" -u "$mysql_user" -p "$mysql_database" -e "SHOW TABLES;"
            ;;
        2)
            local sql_query
            sql_query=$(read_input "Enter SQL query")
            if validate_not_empty "$sql_query" "SQL query"; then
                mysql -h "$mysql_host" -u "$mysql_user" -p "$mysql_database" -e "$sql_query"
            fi
            ;;
        3)
            mysql -h "$mysql_host" -u "$mysql_user" -p -e "SELECT table_schema AS 'Database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' FROM information_schema.tables WHERE table_schema='$mysql_database';"
            ;;
        4)
            mysql -h "$mysql_host" -u "$mysql_user" -p -e "SHOW PROCESSLIST;"
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

mongodb_operations() {
    info_message "MongoDB Operations"
    
    local mongo_uri
    local mongo_database
    
    mongo_uri=$(read_input "Enter MongoDB connection string")
    if ! validate_not_empty "$mongo_uri" "MongoDB connection string"; then
        press_enter
        return
    fi
    
    mongo_database=$(read_input "Enter database name")
    if ! validate_not_empty "$mongo_database" "Database name"; then
        press_enter
        return
    fi
    
    local mongo_options=(
        "List collections"
        "Database stats"
        "Execute custom query"
    )
    
    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!mongo_options[@]}"; do
        echo "$((i+1)). ${mongo_options[$i]}"
    done
    
    read -r mongo_choice
    
    case $mongo_choice in
        1) 
            mongosh "$mongo_uri/$mongo_database" --eval "db.listCollections().forEach(printjson)"
            ;;
        2)
            mongosh "$mongo_uri/$mongo_database" --eval "db.stats()"
            ;;
        3)
            local mongo_query
            mongo_query=$(read_input "Enter MongoDB query")
            if validate_not_empty "$mongo_query" "MongoDB query"; then
                mongosh "$mongo_uri/$mongo_database" --eval "$mongo_query"
            fi
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

database_backup() {
    info_message "Database Backup"
    
    local db_types=(
        "PostgreSQL"
        "MySQL"
        "MongoDB"
    )
    
    echo -e "${CYAN}Select database type:${NC}"
    for i in "${!db_types[@]}"; do
        echo "$((i+1)). ${db_types[$i]}"
    done
    
    read -r db_type
    
    case $db_type in
        1)
            _backup_postgresql
            ;;
        2)
            _backup_mysql
            ;;
        3)
            _backup_mongodb
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

_backup_postgresql() {
    info_message "PostgreSQL Backup"
    
    local pg_host
    local pg_database
    local pg_user
    local backup_file
    
    pg_host=$(read_input "Enter PostgreSQL host")
    pg_database=$(read_input "Enter database name")
    pg_user=$(read_input "Enter username")
    backup_file=$(read_input "Enter backup filename")
    
    if validate_not_empty "$pg_host" "Host" && \
       validate_not_empty "$pg_database" "Database" && \
       validate_not_empty "$pg_user" "Username" && \
       validate_not_empty "$backup_file" "Backup filename"; then
        
        if confirm_action; then
            execute_with_logging "pg_dump -h '$pg_host' -U '$pg_user' '$pg_database' > '$backup_file'" \
                                "Creating PostgreSQL backup: $backup_file"
        fi
    fi
}

_backup_mysql() {
    info_message "MySQL Backup"
    
    local mysql_host
    local mysql_database
    local mysql_user
    local backup_file
    
    mysql_host=$(read_input "Enter MySQL host")
    mysql_database=$(read_input "Enter database name")
    mysql_user=$(read_input "Enter username")
    backup_file=$(read_input "Enter backup filename")
    
    if validate_not_empty "$mysql_host" "Host" && \
       validate_not_empty "$mysql_database" "Database" && \
       validate_not_empty "$mysql_user" "Username" && \
       validate_not_empty "$backup_file" "Backup filename"; then
        
        if confirm_action; then
            execute_with_logging "mysqldump -h '$mysql_host' -u '$mysql_user' -p '$mysql_database' > '$backup_file'" \
                                "Creating MySQL backup: $backup_file"
        fi
    fi
}

_backup_mongodb() {
    info_message "MongoDB Backup"
    
    local mongo_uri
    local mongo_database
    local backup_dir
    
    mongo_uri=$(read_input "Enter MongoDB connection URI")
    mongo_database=$(read_input "Enter database name")
    backup_dir=$(read_input "Enter backup directory")
    
    if validate_not_empty "$mongo_uri" "Connection URI" && \
       validate_not_empty "$mongo_database" "Database" && \
       validate_not_empty "$backup_dir" "Backup directory"; then
        
        if confirm_action; then
            execute_with_logging "mongodump --uri='$mongo_uri' --db='$mongo_database' --out='$backup_dir'" \
                                "Creating MongoDB backup: $backup_dir"
        fi
    fi
}

database_restore() {
    info_message "Database Restore"
    warning_message "WARNING: This will overwrite existing data"
    
    local db_types=(
        "PostgreSQL"
        "MySQL"
        "MongoDB"
    )
    
    echo -e "${CYAN}Select database type:${NC}"
    for i in "${!db_types[@]}"; do
        echo "$((i+1)). ${db_types[$i]}"
    done
    
    read -r db_type
    
    case $db_type in
        1)
            _restore_postgresql
            ;;
        2)
            _restore_mysql
            ;;
        3)
            _restore_mongodb
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

_restore_postgresql() {
    info_message "PostgreSQL Restore"
    
    local pg_host
    local pg_database
    local pg_user
    local backup_file
    
    pg_host=$(read_input "Enter PostgreSQL host")
    pg_database=$(read_input "Enter database name")
    pg_user=$(read_input "Enter username")
    backup_file=$(read_input "Enter backup filename")
    
    if ! validate_file_exists "$backup_file" "Backup file"; then
        return
    fi
    
    if validate_not_empty "$pg_host" "Host" && \
       validate_not_empty "$pg_database" "Database" && \
       validate_not_empty "$pg_user" "Username"; then
        
        if confirm_action; then
            execute_with_logging "psql -h '$pg_host' -U '$pg_user' '$pg_database' < '$backup_file'" \
                                "Restoring PostgreSQL from: $backup_file"
        fi
    fi
}

_restore_mysql() {
    info_message "MySQL Restore"
    
    local mysql_host
    local mysql_database
    local mysql_user
    local backup_file
    
    mysql_host=$(read_input "Enter MySQL host")
    mysql_database=$(read_input "Enter database name")
    mysql_user=$(read_input "Enter username")
    backup_file=$(read_input "Enter backup filename")
    
    if ! validate_file_exists "$backup_file" "Backup file"; then
        return
    fi
    
    if validate_not_empty "$mysql_host" "Host" && \
       validate_not_empty "$mysql_database" "Database" && \
       validate_not_empty "$mysql_user" "Username"; then
        
        if confirm_action; then
            execute_with_logging "mysql -h '$mysql_host' -u '$mysql_user' -p '$mysql_database' < '$backup_file'" \
                                "Restoring MySQL from: $backup_file"
        fi
    fi
}

_restore_mongodb() {
    info_message "MongoDB Restore"
    
    local mongo_uri
    local mongo_database
    local backup_dir
    
    mongo_uri=$(read_input "Enter MongoDB connection URI")
    mongo_database=$(read_input "Enter database name")
    backup_dir=$(read_input "Enter backup directory")
    
    if ! validate_directory_exists "$backup_dir" "Backup directory"; then
        return
    fi
    
    if validate_not_empty "$mongo_uri" "Connection URI" && \
       validate_not_empty "$mongo_database" "Database"; then
        
        if confirm_action; then
            execute_with_logging "mongorestore --uri='$mongo_uri' --db='$mongo_database' '$backup_dir/$mongo_database'" \
                                "Restoring MongoDB from: $backup_dir"
        fi
    fi
}

database_connection_test() {
    info_message "Database Connection Test"
    
    local db_types=(
        "PostgreSQL"
        "MySQL"
        "MongoDB"
    )
    
    echo -e "${CYAN}Select database type:${NC}"
    for i in "${!db_types[@]}"; do
        echo "$((i+1)). ${db_types[$i]}"
    done
    
    read -r db_type
    
    case $db_type in
        1)
            _test_postgresql_connection
            ;;
        2)
            _test_mysql_connection
            ;;
        3)
            _test_mongodb_connection
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

_test_postgresql_connection() {
    local pg_host
    local pg_database
    local pg_user
    
    pg_host=$(read_input "Enter PostgreSQL host")
    pg_database=$(read_input "Enter database name")
    pg_user=$(read_input "Enter username")
    
    info_message "Testing PostgreSQL connection..."
    if psql -h "$pg_host" -U "$pg_user" -d "$pg_database" -c "SELECT 1;" >/dev/null 2>&1; then
        success_message "PostgreSQL connection successful"
    else
        error_message "PostgreSQL connection failed"
    fi
}

_test_mysql_connection() {
    local mysql_host
    local mysql_database
    local mysql_user
    
    mysql_host=$(read_input "Enter MySQL host")
    mysql_database=$(read_input "Enter database name")
    mysql_user=$(read_input "Enter username")
    
    info_message "Testing MySQL connection..."
    if mysql -h "$mysql_host" -u "$mysql_user" -p -e "SELECT 1;" >/dev/null 2>&1; then
        success_message "MySQL connection successful"
    else
        error_message "MySQL connection failed"
    fi
}

_test_mongodb_connection() {
    local mongo_uri
    
    mongo_uri=$(read_input "Enter MongoDB URI")
    
    info_message "Testing MongoDB connection..."
    if mongosh "$mongo_uri" --eval "db.runCommand({ping: 1})" >/dev/null 2>&1; then
        success_message "MongoDB connection successful"
    else
        error_message "MongoDB connection failed"
    fi
}

schema_migration() {
    info_message "Schema Migration"
    
    local migration_path
    migration_path=$(read_input "Enter migration script path")
    
    if ! validate_file_exists "$migration_path" "Migration script"; then
        press_enter
        return
    fi
    
    local db_types=(
        "PostgreSQL"
        "MySQL"
    )
    
    echo -e "${CYAN}Select database type:${NC}"
    for i in "${!db_types[@]}"; do
        echo "$((i+1)). ${db_types[$i]}"
    done
    
    read -r db_type
    
    case $db_type in
        1)
            _migrate_postgresql "$migration_path"
            ;;
        2)
            _migrate_mysql "$migration_path"
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

_migrate_postgresql() {
    local migration_path="$1"
    local pg_host
    local pg_database
    local pg_user
    
    pg_host=$(read_input "Enter PostgreSQL host")
    pg_database=$(read_input "Enter database name")
    pg_user=$(read_input "Enter username")
    
    if validate_not_empty "$pg_host" "Host" && \
       validate_not_empty "$pg_database" "Database" && \
       validate_not_empty "$pg_user" "Username"; then
        
        if confirm_action; then
            execute_with_logging "psql -h '$pg_host' -U '$pg_user' -d '$pg_database' -f '$migration_path'" \
                                "Running PostgreSQL migration: $migration_path"
        fi
    fi
}

_migrate_mysql() {
    local migration_path="$1"
    local mysql_host
    local mysql_database
    local mysql_user
    
    mysql_host=$(read_input "Enter MySQL host")
    mysql_database=$(read_input "Enter database name")
    mysql_user=$(read_input "Enter username")
    
    if validate_not_empty "$mysql_host" "Host" && \
       validate_not_empty "$mysql_database" "Database" && \
       validate_not_empty "$mysql_user" "Username"; then
        
        if confirm_action; then
            execute_with_logging "mysql -h '$mysql_host' -u '$mysql_user' -p '$mysql_database' < '$migration_path'" \
                                "Running MySQL migration: $migration_path"
        fi
    fi
}

# Export functions
export -f database_menu postgresql_operations mysql_operations mongodb_operations
export -f database_backup database_restore database_connection_test schema_migration
