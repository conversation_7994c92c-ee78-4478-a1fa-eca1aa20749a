#!/bin/bash

# Azure Operations Module for DOCMS Task Runner
# Contains all Azure-related functions

# Source utilities
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# Category 6: Azure Operations
azure_menu() {
    local options=(
        "Azure Login & Account Info"
        "Key Vault Operations"
        "Resource Group Management"
        "Virtual Machine Operations"
        "Storage Account Operations"
        "App Service Operations"
        "Container Registry Operations"
        "Kubernetes Service (AKS) Operations"
        "Azure DevOps Operations"
    )

    local functions=(
        "azure_login_info"
        "azure_keyvault_operations"
        "azure_resource_group_operations"
        "azure_vm_operations"
        "azure_storage_operations"
        "azure_app_service_operations"
        "azure_container_registry_operations"
        "azure_aks_operations"
        "azure_devops_operations"
    )

    while true; do
        show_menu_options "AZURE OPERATIONS" "${options[@]}"
        read -r choice

        if ! handle_menu_choice "$choice" "${#options[@]}" "${functions[@]}"; then
            break
        fi
    done
}

azure_login_info() {
    info_message "Azure Login & Account Information"

    info_message "Checking Azure CLI installation..."
    if ! check_tool_available "az" "Azure CLI"; then
        error_message "Azure CLI is not installed"
        info_message "Install Azure CLI: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
        press_enter
        return
    fi

    success_message "Azure CLI is installed"
    az --version | head -1

    echo
    info_message "Current login status:"
    if az account show &>/dev/null; then
        success_message "Already logged in"
        az account show --output table

        echo
        echo -e "${CYAN}List all subscriptions? (y/N): ${NC}"
        read -r list_subs
        if [[ "$list_subs" =~ ^[yY]$ ]]; then
            az account list --output table
        fi
    else
        warning_message "Not logged in to Azure"
        echo -e "${CYAN}Login now? (y/N): ${NC}"
        read -r do_login
        if [[ "$do_login" =~ ^[yY]$ ]]; then
            az login
        fi
    fi

    press_enter
}

azure_keyvault_operations() {
    info_message "Azure Key Vault Operations"

    local keyvault_name

    # Option to choose from available Key Vaults
    echo -e "${CYAN}How would you like to select the Key Vault?${NC}"
    echo "1. Choose from available Key Vaults"
    echo "2. Enter Key Vault name manually"
    echo ""
    echo -e "${CYAN}Enter your choice [1-2]: ${NC}"
    read -r selection_method

    case $selection_method in
        1)
            keyvault_name=$(_select_keyvault_from_list)
            if [[ $? -ne 0 ]] || [[ -z "$keyvault_name" ]]; then
                error_message "Failed to select Key Vault from list"
                press_enter
                return
            fi
            ;;
        2)
            keyvault_name=$(read_input "Enter Key Vault name")
            if ! validate_not_empty "$keyvault_name" "Key Vault name"; then
                press_enter
                return
            fi
            ;;
        *)
            error_message "Invalid selection"
            press_enter
            return
            ;;
    esac

    info_message "Selected Key Vault: $keyvault_name"

    local kv_options=(
        "List all secrets"
        "Get secret value"
        "Set secret"
        "Delete secret"
        "List all keys"
        "Key Vault information"
    )

    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!kv_options[@]}"; do
        echo "$((i+1)). ${kv_options[$i]}"
    done

    read -r kv_operation

    case $kv_operation in
        1)
            info_message "Listing all secrets in $keyvault_name..."
            az keyvault secret list --vault-name "$keyvault_name" --output table
            ;;
        2)
            local secret_name
            secret_name=$(read_input "Enter secret name")
            if validate_not_empty "$secret_name" "Secret name"; then
                info_message "Getting secret value..."
                az keyvault secret show --vault-name "$keyvault_name" --name "$secret_name" --query "value" --output tsv
            fi
            ;;
        3)
            local secret_name
            local secret_value
            secret_name=$(read_input "Enter secret name")
            secret_value=$(read_password "Enter secret value")

            if validate_not_empty "$secret_name" "Secret name" && \
               validate_not_empty "$secret_value" "Secret value" && \
               confirm_action; then
                execute_with_logging "az keyvault secret set --vault-name '$keyvault_name' --name '$secret_name' --value '$secret_value'" \
                                    "Setting secret $secret_name in Key Vault $keyvault_name"
            fi
            ;;
        4)
            local secret_name
            secret_name=$(read_input "Enter secret name to delete")
            if validate_not_empty "$secret_name" "Secret name"; then
                warning_message "WARNING: This will permanently delete the secret"
                if confirm_action; then
                    execute_with_logging "az keyvault secret delete --vault-name '$keyvault_name' --name '$secret_name'" \
                                        "Deleting secret $secret_name from Key Vault $keyvault_name"
                fi
            fi
            ;;
        5)
            info_message "Listing all keys in $keyvault_name..."
            az keyvault key list --vault-name "$keyvault_name" --output table
            ;;
        6)
            info_message "Key Vault information for $keyvault_name..."
            az keyvault show --name "$keyvault_name" --output table
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

# Helper function to select Key Vault from available list
_select_keyvault_from_list() {
    info_message "Fetching available Key Vaults..."

    # Check if Azure CLI is available
    if ! command -v az &> /dev/null; then
        error_message "Azure CLI is not installed or not in PATH"
        warning_message "Please install Azure CLI: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
        return 1
    fi

    # Check if logged in to Azure
    if ! az account show &>/dev/null; then
        error_message "Not logged in to Azure"
        warning_message "Please run 'az login' first"
        return 1
    fi

    # Get list of Key Vaults
    local keyvaults_json
    keyvaults_json=$(az keyvault list --query "[].{Name:name, ResourceGroup:resourceGroup, Location:location}" --output json 2>/dev/null)

    if [[ $? -ne 0 ]] || [[ -z "$keyvaults_json" ]] || [[ "$keyvaults_json" == "[]" ]]; then
        error_message "Failed to fetch Key Vaults or no Key Vaults found"
        warning_message "Make sure you have access to Key Vaults in your current subscription"
        info_message "You can check your current subscription with: az account show"
        return 1
    fi

    # Parse and display Key Vaults
    local keyvault_names
    local keyvault_details

    # Extract names and details
    keyvault_names=($(echo "$keyvaults_json" | grep -o '"Name":"[^"]*"' | cut -d'"' -f4))

    if [[ ${#keyvault_names[@]} -eq 0 ]]; then
        error_message "No Key Vaults found in your subscription"
        return 1
    fi

    echo -e "${GREEN}Available Key Vaults:${NC}"
    echo ""

    # Display Key Vaults with details
    local i=1
    for name in "${keyvault_names[@]}"; do
        if command -v jq &> /dev/null; then
            local rg=$(echo "$keyvaults_json" | jq -r ".[] | select(.Name==\"$name\") | .ResourceGroup")
            local location=$(echo "$keyvaults_json" | jq -r ".[] | select(.Name==\"$name\") | .Location")
            echo -e "${YELLOW}$i.${NC} ${CYAN}$name${NC} (RG: $rg, Location: $location)"
        else
            # Fallback if jq is not available
            echo -e "${YELLOW}$i.${NC} ${CYAN}$name${NC}"
        fi
        ((i++))
    done

    echo -e "${YELLOW}0.${NC} Cancel"
    echo ""
    echo -e "${CYAN}Select a Key Vault [0-$((${#keyvault_names[@]}))]: ${NC}"

    read -r choice

    if [[ "$choice" == "0" ]]; then
        warning_message "Operation cancelled"
        return 1
    elif [[ "$choice" =~ ^[1-9][0-9]*$ ]] && [[ "$choice" -le "${#keyvault_names[@]}" ]]; then
        local selected_keyvault="${keyvault_names[$((choice-1))]}"
        success_message "Selected Key Vault: $selected_keyvault"
        echo "$selected_keyvault"
        return 0
    else
        error_message "Invalid selection"
        return 1
    fi
}

azure_resource_group_operations() {
    info_message "Azure Resource Group Management"

    local rg_options=(
        "List all resource groups"
        "Create resource group"
        "Delete resource group"
        "Show resource group details"
        "List resources in group"
    )

    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!rg_options[@]}"; do
        echo "$((i+1)). ${rg_options[$i]}"
    done

    read -r rg_operation

    case $rg_operation in
        1)
            info_message "Listing all resource groups..."
            az group list --output table
            ;;
        2)
            local rg_name
            local location
            rg_name=$(read_input "Enter resource group name")
            location=$(read_input "Enter location (e.g., eastus, westeurope)")

            if validate_not_empty "$rg_name" "Resource group name" && \
               validate_not_empty "$location" "Location" && \
               confirm_action; then
                execute_with_logging "az group create --name '$rg_name' --location '$location'" \
                                    "Creating resource group $rg_name in $location"
            fi
            ;;
        3)
            local rg_name
            rg_name=$(read_input "Enter resource group name to delete")
            if validate_not_empty "$rg_name" "Resource group name"; then
                warning_message "WARNING: This will delete ALL resources in the group"
                if confirm_action; then
                    execute_with_logging "az group delete --name '$rg_name' --yes" \
                                        "Deleting resource group $rg_name"
                fi
            fi
            ;;
        4)
            local rg_name
            rg_name=$(read_input "Enter resource group name")
            if validate_not_empty "$rg_name" "Resource group name"; then
                info_message "Resource group details for $rg_name..."
                az group show --name "$rg_name" --output table
            fi
            ;;
        5)
            local rg_name
            rg_name=$(read_input "Enter resource group name")
            if validate_not_empty "$rg_name" "Resource group name"; then
                info_message "Resources in $rg_name..."
                az resource list --resource-group "$rg_name" --output table
            fi
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

azure_vm_operations() {
    info_message "Azure Virtual Machine Operations"

    local vm_options=(
        "List all VMs"
        "Start VM"
        "Stop VM"
        "Restart VM"
        "Get VM status"
        "Create VM"
        "Delete VM"
    )

    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!vm_options[@]}"; do
        echo "$((i+1)). ${vm_options[$i]}"
    done

    read -r vm_operation

    case $vm_operation in
        1)
            info_message "Listing all virtual machines..."
            az vm list --output table
            ;;
        2|3|4|5|7)
            local rg_name
            local vm_name
            rg_name=$(read_input "Enter resource group name")
            vm_name=$(read_input "Enter VM name")

            if validate_not_empty "$rg_name" "Resource group name" && \
               validate_not_empty "$vm_name" "VM name"; then

                case $vm_operation in
                    2)
                        if confirm_action; then
                            execute_with_logging "az vm start --resource-group '$rg_name' --name '$vm_name'" \
                                                "Starting VM $vm_name in resource group $rg_name"
                        fi
                        ;;
                    3)
                        if confirm_action; then
                            execute_with_logging "az vm stop --resource-group '$rg_name' --name '$vm_name'" \
                                                "Stopping VM $vm_name in resource group $rg_name"
                        fi
                        ;;
                    4)
                        if confirm_action; then
                            execute_with_logging "az vm restart --resource-group '$rg_name' --name '$vm_name'" \
                                                "Restarting VM $vm_name in resource group $rg_name"
                        fi
                        ;;
                    5)
                        info_message "VM status for $vm_name..."
                        az vm get-instance-view --resource-group "$rg_name" --name "$vm_name" --output table
                        ;;
                    7)
                        warning_message "WARNING: This will permanently delete the VM"
                        if confirm_action; then
                            execute_with_logging "az vm delete --resource-group '$rg_name' --name '$vm_name' --yes" \
                                                "Deleting VM $vm_name from resource group $rg_name"
                        fi
                        ;;
                esac
            fi
            ;;
        6)
            _create_azure_vm
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

_create_azure_vm() {
    local rg_name
    local vm_name
    local vm_size
    local admin_username

    rg_name=$(read_input "Enter resource group name")
    vm_name=$(read_input "Enter VM name")
    vm_size=$(read_input "Enter VM size (e.g., Standard_B1s)")
    admin_username=$(read_input "Enter admin username")

    if validate_not_empty "$rg_name" "Resource group name" && \
       validate_not_empty "$vm_name" "VM name" && \
       validate_not_empty "$vm_size" "VM size" && \
       validate_not_empty "$admin_username" "Admin username" && \
       confirm_action; then
        execute_with_logging "az vm create --resource-group '$rg_name' --name '$vm_name' --size '$vm_size' --admin-username '$admin_username' --generate-ssh-keys --output table" \
                            "Creating VM $vm_name in resource group $rg_name"
    fi
}

azure_storage_operations() {
    info_message "Azure Storage Account Operations"

    local storage_options=(
        "List storage accounts"
        "Create storage account"
        "Get storage account keys"
        "List containers"
        "Upload blob"
        "Download blob"
    )

    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!storage_options[@]}"; do
        echo "$((i+1)). ${storage_options[$i]}"
    done

    read -r storage_operation

    case $storage_operation in
        1)
            info_message "Listing all storage accounts..."
            az storage account list --output table
            ;;
        2)
            _create_storage_account
            ;;
        3)
            _get_storage_keys
            ;;
        4)
            _list_containers
            ;;
        5)
            _upload_blob
            ;;
        6)
            _download_blob
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

_create_storage_account() {
    local rg_name
    local storage_name
    local location

    rg_name=$(read_input "Enter resource group name")
    storage_name=$(read_input "Enter storage account name")
    location=$(read_input "Enter location")

    if validate_not_empty "$rg_name" "Resource group name" && \
       validate_not_empty "$storage_name" "Storage account name" && \
       validate_not_empty "$location" "Location" && \
       confirm_action; then
        execute_with_logging "az storage account create --name '$storage_name' --resource-group '$rg_name' --location '$location' --sku Standard_LRS" \
                            "Creating storage account $storage_name"
    fi
}

_get_storage_keys() {
    local rg_name
    local storage_name

    rg_name=$(read_input "Enter resource group name")
    storage_name=$(read_input "Enter storage account name")

    if validate_not_empty "$rg_name" "Resource group name" && \
       validate_not_empty "$storage_name" "Storage account name"; then
        info_message "Getting storage account keys..."
        az storage account keys list --resource-group "$rg_name" --account-name "$storage_name" --output table
    fi
}

_list_containers() {
    local storage_name
    storage_name=$(read_input "Enter storage account name")

    if validate_not_empty "$storage_name" "Storage account name"; then
        info_message "Listing containers..."
        az storage container list --account-name "$storage_name" --output table
    fi
}

_upload_blob() {
    local storage_name
    local container_name
    local local_file
    local blob_name

    storage_name=$(read_input "Enter storage account name")
    container_name=$(read_input "Enter container name")
    local_file=$(read_input "Enter local file path")
    blob_name=$(read_input "Enter blob name")

    if validate_not_empty "$storage_name" "Storage account name" && \
       validate_not_empty "$container_name" "Container name" && \
       validate_file_exists "$local_file" "Local file" && \
       validate_not_empty "$blob_name" "Blob name" && \
       confirm_action; then
        execute_with_logging "az storage blob upload --account-name '$storage_name' --container-name '$container_name' --name '$blob_name' --file '$local_file'" \
                            "Uploading $local_file to blob $blob_name"
    fi
}

_download_blob() {
    local storage_name
    local container_name
    local blob_name
    local local_file

    storage_name=$(read_input "Enter storage account name")
    container_name=$(read_input "Enter container name")
    blob_name=$(read_input "Enter blob name")
    local_file=$(read_input "Enter local file path to save")

    if validate_not_empty "$storage_name" "Storage account name" && \
       validate_not_empty "$container_name" "Container name" && \
       validate_not_empty "$blob_name" "Blob name" && \
       validate_not_empty "$local_file" "Local file path" && \
       confirm_action; then
        execute_with_logging "az storage blob download --account-name '$storage_name' --container-name '$container_name' --name '$blob_name' --file '$local_file'" \
                            "Downloading blob $blob_name to $local_file"
    fi
}

azure_app_service_operations() {
    info_message "Azure App Service Operations"

    local app_options=(
        "List app services"
        "Create app service"
        "Deploy from Git"
        "Start app service"
        "Stop app service"
        "Get app service logs"
    )

    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!app_options[@]}"; do
        echo "$((i+1)). ${app_options[$i]}"
    done

    read -r app_operation

    case $app_operation in
        1)
            info_message "Listing all app services..."
            az webapp list --output table
            ;;
        2)
            _create_app_service
            ;;
        3)
            _deploy_from_git
            ;;
        4|5)
            local rg_name
            local app_name
            rg_name=$(read_input "Enter resource group name")
            app_name=$(read_input "Enter app service name")

            if validate_not_empty "$rg_name" "Resource group name" && \
               validate_not_empty "$app_name" "App service name" && \
               confirm_action; then

                if [[ "$app_operation" == "4" ]]; then
                    execute_with_logging "az webapp start --resource-group '$rg_name' --name '$app_name'" \
                                        "Starting app service $app_name"
                else
                    execute_with_logging "az webapp stop --resource-group '$rg_name' --name '$app_name'" \
                                        "Stopping app service $app_name"
                fi
            fi
            ;;
        6)
            local rg_name
            local app_name
            rg_name=$(read_input "Enter resource group name")
            app_name=$(read_input "Enter app service name")

            if validate_not_empty "$rg_name" "Resource group name" && \
               validate_not_empty "$app_name" "App service name"; then
                info_message "Getting app service logs..."
                az webapp log tail --resource-group "$rg_name" --name "$app_name"
            fi
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

_create_app_service() {
    local rg_name
    local app_name
    local plan_name

    rg_name=$(read_input "Enter resource group name")
    app_name=$(read_input "Enter app service name")
    plan_name=$(read_input "Enter app service plan name")

    if validate_not_empty "$rg_name" "Resource group name" && \
       validate_not_empty "$app_name" "App service name" && \
       validate_not_empty "$plan_name" "App service plan name" && \
       confirm_action; then
        execute_with_logging "az webapp create --resource-group '$rg_name' --plan '$plan_name' --name '$app_name'" \
                            "Creating app service $app_name"
    fi
}

_deploy_from_git() {
    local rg_name
    local app_name
    local git_url

    rg_name=$(read_input "Enter resource group name")
    app_name=$(read_input "Enter app service name")
    git_url=$(read_input "Enter Git repository URL")

    if validate_not_empty "$rg_name" "Resource group name" && \
       validate_not_empty "$app_name" "App service name" && \
       validate_not_empty "$git_url" "Git URL" && \
       confirm_action; then
        execute_with_logging "az webapp deployment source config --resource-group '$rg_name' --name '$app_name' --repo-url '$git_url' --branch master --manual-integration" \
                            "Deploying $git_url to app service $app_name"
    fi
}

azure_container_registry_operations() {
    info_message "Azure Container Registry Operations"

    local acr_options=(
        "List container registries"
        "Create container registry"
        "Login to registry"
        "List repositories"
        "Build and push image"
    )

    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!acr_options[@]}"; do
        echo "$((i+1)). ${acr_options[$i]}"
    done

    read -r acr_operation

    case $acr_operation in
        1)
            info_message "Listing all container registries..."
            az acr list --output table
            ;;
        2)
            _create_container_registry
            ;;
        3)
            local registry_name
            registry_name=$(read_input "Enter registry name")
            if validate_not_empty "$registry_name" "Registry name"; then
                execute_with_logging "az acr login --name '$registry_name'" "Logging in to container registry $registry_name"
            fi
            ;;
        4)
            local registry_name
            registry_name=$(read_input "Enter registry name")
            if validate_not_empty "$registry_name" "Registry name"; then
                info_message "Listing repositories..."
                az acr repository list --name "$registry_name" --output table
            fi
            ;;
        5)
            _build_and_push_image
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

_create_container_registry() {
    local rg_name
    local registry_name
    local sku

    rg_name=$(read_input "Enter resource group name")
    registry_name=$(read_input "Enter registry name")
    sku=$(read_input "Enter SKU (Basic/Standard/Premium)")

    if validate_not_empty "$rg_name" "Resource group name" && \
       validate_not_empty "$registry_name" "Registry name" && \
       validate_not_empty "$sku" "SKU" && \
       confirm_action; then
        execute_with_logging "az acr create --resource-group '$rg_name' --name '$registry_name' --sku '$sku'" \
                            "Creating container registry $registry_name"
    fi
}

_build_and_push_image() {
    local registry_name
    local image_name
    local dockerfile_path

    registry_name=$(read_input "Enter registry name")
    image_name=$(read_input "Enter image name")
    dockerfile_path=$(read_input "Enter Dockerfile path" ".")

    if validate_not_empty "$registry_name" "Registry name" && \
       validate_not_empty "$image_name" "Image name" && \
       validate_directory_exists "$dockerfile_path" "Dockerfile path" && \
       confirm_action; then
        execute_with_logging "az acr build --registry '$registry_name' --image '$image_name' '$dockerfile_path'" \
                            "Building and pushing image $image_name to registry $registry_name"
    fi
}

azure_aks_operations() {
    info_message "Azure Kubernetes Service (AKS) Operations"

    local aks_options=(
        "List AKS clusters"
        "Create AKS cluster"
        "Get AKS credentials"
        "Scale AKS cluster"
        "Start AKS cluster"
        "Stop AKS cluster"
    )

    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!aks_options[@]}"; do
        echo "$((i+1)). ${aks_options[$i]}"
    done

    read -r aks_operation

    case $aks_operation in
        1)
            info_message "Listing all AKS clusters..."
            az aks list --output table
            ;;
        2)
            _create_aks_cluster
            ;;
        3|4|5|6)
            local rg_name
            local cluster_name
            rg_name=$(read_input "Enter resource group name")
            cluster_name=$(read_input "Enter cluster name")

            if validate_not_empty "$rg_name" "Resource group name" && \
               validate_not_empty "$cluster_name" "Cluster name"; then

                case $aks_operation in
                    3)
                        execute_with_logging "az aks get-credentials --resource-group '$rg_name' --name '$cluster_name'" \
                                            "Getting AKS credentials for $cluster_name"
                        ;;
                    4)
                        local node_count
                        node_count=$(read_input "Enter new node count")
                        if validate_not_empty "$node_count" "Node count" && confirm_action; then
                            execute_with_logging "az aks scale --resource-group '$rg_name' --name '$cluster_name' --node-count '$node_count'" \
                                                "Scaling AKS cluster $cluster_name to $node_count nodes"
                        fi
                        ;;
                    5)
                        if confirm_action; then
                            execute_with_logging "az aks start --resource-group '$rg_name' --name '$cluster_name'" \
                                                "Starting AKS cluster $cluster_name"
                        fi
                        ;;
                    6)
                        if confirm_action; then
                            execute_with_logging "az aks stop --resource-group '$rg_name' --name '$cluster_name'" \
                                                "Stopping AKS cluster $cluster_name"
                        fi
                        ;;
                esac
            fi
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

_create_aks_cluster() {
    local rg_name
    local cluster_name
    local node_count

    rg_name=$(read_input "Enter resource group name")
    cluster_name=$(read_input "Enter cluster name")
    node_count=$(read_input "Enter node count" "3")

    if validate_not_empty "$rg_name" "Resource group name" && \
       validate_not_empty "$cluster_name" "Cluster name" && \
       validate_not_empty "$node_count" "Node count" && \
       confirm_action; then
        execute_with_logging "az aks create --resource-group '$rg_name' --name '$cluster_name' --node-count '$node_count' --enable-addons monitoring --generate-ssh-keys" \
                            "Creating AKS cluster $cluster_name"
    fi
}

azure_devops_operations() {
    info_message "Azure DevOps Operations"

    local devops_options=(
        "List DevOps organizations"
        "List projects"
        "List pipelines"
        "Run pipeline"
        "List repositories"
        "Clone repository"
    )

    echo -e "${CYAN}Select operation:${NC}"
    for i in "${!devops_options[@]}"; do
        echo "$((i+1)). ${devops_options[$i]}"
    done

    read -r devops_operation

    case $devops_operation in
        1)
            info_message "Listing DevOps organizations..."
            az devops project list --output table
            ;;
        2)
            local org_url
            org_url=$(read_input "Enter organization URL")
            if validate_not_empty "$org_url" "Organization URL"; then
                info_message "Listing projects..."
                az devops project list --organization "$org_url" --output table
            fi
            ;;
        3)
            _list_pipelines
            ;;
        4)
            _run_pipeline
            ;;
        5)
            _list_repositories
            ;;
        6)
            _clone_repository
            ;;
        *)
            error_message "Invalid option"
            ;;
    esac
    press_enter
}

_list_pipelines() {
    local org_url
    local project_name

    org_url=$(read_input "Enter organization URL")
    project_name=$(read_input "Enter project name")

    if validate_not_empty "$org_url" "Organization URL" && \
       validate_not_empty "$project_name" "Project name"; then
        info_message "Listing pipelines..."
        az pipelines list --organization "$org_url" --project "$project_name" --output table
    fi
}

_run_pipeline() {
    local org_url
    local project_name
    local pipeline_id

    org_url=$(read_input "Enter organization URL")
    project_name=$(read_input "Enter project name")
    pipeline_id=$(read_input "Enter pipeline ID")

    if validate_not_empty "$org_url" "Organization URL" && \
       validate_not_empty "$project_name" "Project name" && \
       validate_not_empty "$pipeline_id" "Pipeline ID" && \
       confirm_action; then
        execute_with_logging "az pipelines run --organization '$org_url' --project '$project_name' --id '$pipeline_id'" \
                            "Running pipeline $pipeline_id"
    fi
}

_list_repositories() {
    local org_url
    local project_name

    org_url=$(read_input "Enter organization URL")
    project_name=$(read_input "Enter project name")

    if validate_not_empty "$org_url" "Organization URL" && \
       validate_not_empty "$project_name" "Project name"; then
        info_message "Listing repositories..."
        az repos list --organization "$org_url" --project "$project_name" --output table
    fi
}

_clone_repository() {
    local repo_url
    local local_dir

    repo_url=$(read_input "Enter repository URL")
    local_dir=$(read_input "Enter local directory name")

    if validate_not_empty "$repo_url" "Repository URL" && \
       validate_not_empty "$local_dir" "Local directory" && \
       confirm_action; then
        execute_with_logging "git clone '$repo_url' '$local_dir'" \
                            "Cloning repository $repo_url to $local_dir"
    fi
}

# Export functions
export -f azure_menu azure_login_info azure_keyvault_operations azure_resource_group_operations
export -f azure_vm_operations azure_storage_operations azure_app_service_operations
export -f azure_container_registry_operations azure_aks_operations azure_devops_operations
export -f _select_keyvault_from_list