#!/bin/bash

# DOCMS Task Runner Installation Script
# This script helps set up the task runner and its dependencies

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║              DOCMS Task Runner Installation                  ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Check if running on supported OS
if [[ "$OSTYPE" != "linux-gnu"* && "$OSTYPE" != "darwin"* ]]; then
    echo -e "${RED}✗ Unsupported operating system: $OSTYPE${NC}"
    echo -e "${YELLOW}This script supports Linux and macOS only.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Operating system supported: $OSTYPE${NC}"

# Check bash version
bash_version=$(bash --version | head -n1 | grep -oE '[0-9]+\.[0-9]+' | head -1)
if [[ $(echo "$bash_version >= 4.0" | bc -l) -eq 0 ]]; then
    echo -e "${RED}✗ Bash version $bash_version is too old. Requires 4.0+${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Bash version $bash_version is supported${NC}"

# Make task-runner.sh executable
if [[ -f "task-runner.sh" ]]; then
    chmod +x task-runner.sh
    echo -e "${GREEN}✓ Made task-runner.sh executable${NC}"
else
    echo -e "${RED}✗ task-runner.sh not found in current directory${NC}"
    exit 1
fi

# Check for required tools
echo ""
echo -e "${BLUE}Checking for required tools...${NC}"

check_tool() {
    local tool=$1
    local description=$2
    local install_hint=$3
    
    if command -v "$tool" &> /dev/null; then
        echo -e "${GREEN}✓ $description${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠ $description not found${NC}"
        if [[ -n "$install_hint" ]]; then
            echo -e "${CYAN}  Install with: $install_hint${NC}"
        fi
        return 1
    fi
}

# Core tools
check_tool "curl" "curl" "apt-get install curl (Ubuntu) / brew install curl (macOS)"
check_tool "git" "Git" "apt-get install git (Ubuntu) / brew install git (macOS)"

# Build tools
echo ""
echo -e "${BLUE}Checking for build tools...${NC}"
check_tool "docker" "Docker" "https://docs.docker.com/get-docker/"
check_tool "mvn" "Maven" "apt-get install maven (Ubuntu) / brew install maven (macOS)"
check_tool "npm" "Node.js/NPM" "https://nodejs.org/"
check_tool "gradle" "Gradle" "apt-get install gradle (Ubuntu) / brew install gradle (macOS)"

# Kubernetes tools
echo ""
echo -e "${BLUE}Checking for Kubernetes tools...${NC}"
check_tool "kubectl" "kubectl" "https://kubernetes.io/docs/tasks/tools/"

# Database tools
echo ""
echo -e "${BLUE}Checking for database tools...${NC}"
check_tool "psql" "PostgreSQL client" "apt-get install postgresql-client (Ubuntu) / brew install postgresql (macOS)"
check_tool "mysql" "MySQL client" "apt-get install mysql-client (Ubuntu) / brew install mysql-client (macOS)"
check_tool "mongosh" "MongoDB Shell" "https://docs.mongodb.com/mongodb-shell/install/"

# Cloud tools
echo ""
echo -e "${BLUE}Checking for cloud tools...${NC}"
check_tool "az" "Azure CLI" "https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"

# Testing tools
echo ""
echo -e "${BLUE}Checking for testing tools...${NC}"
check_tool "ab" "Apache Bench" "apt-get install apache2-utils (Ubuntu) / brew install httpie (macOS)"

# Create config file if it doesn't exist
if [[ ! -f "config.sh" && -f "config.example.sh" ]]; then
    echo ""
    echo -e "${CYAN}Creating config.sh from example...${NC}"
    cp config.example.sh config.sh
    echo -e "${GREEN}✓ Created config.sh${NC}"
    echo -e "${YELLOW}⚠ Please edit config.sh to customize for your environment${NC}"
fi

# Create symlink for global access (optional)
echo ""
echo -e "${CYAN}Create global symlink? This will allow you to run 'docms-tasks' from anywhere. (y/N): ${NC}"
read -r create_symlink

if [[ "$create_symlink" =~ ^[yY]$ ]]; then
    SCRIPT_PATH="$(pwd)/task-runner.sh"
    SYMLINK_PATH="/usr/local/bin/docms-tasks"
    
    if [[ -w "/usr/local/bin" ]]; then
        ln -sf "$SCRIPT_PATH" "$SYMLINK_PATH"
        echo -e "${GREEN}✓ Created symlink: $SYMLINK_PATH${NC}"
        echo -e "${CYAN}You can now run 'docms-tasks' from anywhere${NC}"
    else
        echo -e "${YELLOW}⚠ Need sudo access to create symlink${NC}"
        echo -e "${CYAN}Run: sudo ln -sf '$SCRIPT_PATH' '$SYMLINK_PATH'${NC}"
    fi
fi

echo ""
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                    Installation Complete!                   ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""
echo -e "${CYAN}To get started:${NC}"
echo -e "${YELLOW}1.${NC} Edit config.sh to customize for your environment"
echo -e "${YELLOW}2.${NC} Run: ./task-runner.sh"
echo -e "${YELLOW}3.${NC} Or run: docms-tasks (if you created the symlink)"
echo ""
echo -e "${CYAN}For help and documentation, see README.md${NC}"
