#!/bin/bash

# DOCMS Task Runner Configuration Example
# Copy this file to config.sh and customize for your environment

# Azure Configuration
export AZURE_DEFAULT_SUBSCRIPTION="your-subscription-id"
export AZURE_DEFAULT_RESOURCE_GROUP="your-default-rg"
export AZURE_DEFAULT_LOCATION="eastus"
export AZURE_DEFAULT_KEYVAULT="your-keyvault-name"

# Kubernetes Configuration
export KUBE_DEFAULT_NAMESPACE="default"
export KUBE_CONFIG_PATH="$HOME/.kube/config"

# Database Configuration
export DB_DEFAULT_HOST="localhost"
export DB_DEFAULT_PORT="5432"
export DB_DEFAULT_USER="admin"

# Docker Configuration
export DOCKER_DEFAULT_REGISTRY="your-registry.azurecr.io"
export DOCKER_DEFAULT_TAG="latest"

# Build Configuration
export MAVEN_OPTS="-Xmx2g"
export NODE_ENV="development"

# Monitoring Configuration
export LOG_RETENTION_DAYS="30"
export ALERT_EMAIL="<EMAIL>"

# Testing Configuration
export TEST_TIMEOUT="300"
export COVERAGE_THRESHOLD="80"

# Custom Functions
# You can add your own custom functions here

custom_deploy_to_staging() {
    echo -e "${GREEN}Deploying to Staging Environment${NC}"
    echo -e "${CYAN}Enter application name: ${NC}"
    read -r app_name
    
    if confirm_action; then
        log "Deploying $app_name to staging"
        # Add your custom deployment logic here
        echo -e "${GREEN}✓ Deployment to staging completed${NC}"
    fi
    press_enter
}

custom_backup_all_databases() {
    echo -e "${GREEN}Backing up All Databases${NC}"
    
    if confirm_action; then
        log "Starting full database backup"
        # Add your custom backup logic here
        echo -e "${GREEN}✓ All databases backed up${NC}"
    fi
    press_enter
}

# Load this configuration in the main script by adding:
# source config.sh 2>/dev/null || true
# at the top of task-runner.sh (after the shebang line)
