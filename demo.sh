#!/bin/bash

# DOCMS Task Runner Demo Script
# This script demonstrates some of the capabilities without requiring user input

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║                DOCMS Task Runner Demo                        ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

demo_section() {
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║ $1${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Demo 1: System Health Check
demo_section "Demo 1: System Health Check"
echo -e "${GREEN}Checking system health...${NC}"
echo -e "${CYAN}Hostname:${NC} $(hostname)"
echo -e "${CYAN}Uptime:${NC} $(uptime)"
echo -e "${CYAN}Load Average:${NC} $(uptime | awk -F'load average:' '{print $2}')"
echo ""
echo -e "${BLUE}Memory Usage:${NC}"
free -h 2>/dev/null || echo "Memory info not available on this system"
echo ""
echo -e "${BLUE}Disk Usage:${NC}"
df -h | head -5
echo ""
sleep 2

# Demo 2: Build Status Check
demo_section "Demo 2: Build Environment Check"
echo -e "${BLUE}Checking build tools availability...${NC}"

check_tool() {
    local tool=$1
    local description=$2
    
    if command -v "$tool" &> /dev/null; then
        echo -e "${GREEN}✓ $description available${NC}"
        $tool --version 2>/dev/null | head -1 || echo "  Version info not available"
    else
        echo -e "${RED}✗ $description not available${NC}"
    fi
}

check_tool "docker" "Docker"
check_tool "mvn" "Maven"
check_tool "npm" "Node.js/NPM"
check_tool "gradle" "Gradle"
check_tool "kubectl" "Kubernetes CLI"
check_tool "az" "Azure CLI"
echo ""
sleep 2

# Demo 3: Network Connectivity Test
demo_section "Demo 3: Network Connectivity Test"
echo -e "${BLUE}Testing network connectivity...${NC}"

test_connectivity() {
    local host=$1
    local description=$2
    
    echo -n -e "${CYAN}Testing $description ($host)... ${NC}"
    if ping -c 1 -W 3 "$host" &>/dev/null; then
        echo -e "${GREEN}✓ Reachable${NC}"
    else
        echo -e "${RED}✗ Unreachable${NC}"
    fi
}

test_connectivity "*******" "Google DNS"
test_connectivity "github.com" "GitHub"
test_connectivity "docker.io" "Docker Hub"
echo ""
sleep 2

# Demo 4: Docker Status (if available)
if command -v docker &> /dev/null; then
    demo_section "Demo 4: Docker Status"
    echo -e "${BLUE}Docker system information...${NC}"
    
    if docker info &>/dev/null; then
        echo -e "${GREEN}✓ Docker daemon is running${NC}"
        echo ""
        echo -e "${BLUE}Running containers:${NC}"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Image}}" 2>/dev/null || echo "No containers running"
        echo ""
        echo -e "${BLUE}Docker images:${NC}"
        docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" 2>/dev/null | head -5 || echo "No images found"
    else
        echo -e "${YELLOW}⚠ Docker daemon is not running${NC}"
    fi
    echo ""
    sleep 2
fi

# Demo 5: Process Monitoring
demo_section "Demo 5: Process Monitoring"
echo -e "${BLUE}Top processes by CPU usage:${NC}"
ps aux --sort=-%cpu | head -6 2>/dev/null || echo "Process info not available"
echo ""
echo -e "${BLUE}Top processes by memory usage:${NC}"
ps aux --sort=-%mem | head -6 2>/dev/null || echo "Process info not available"
echo ""
sleep 2

# Demo 6: Log Analysis
demo_section "Demo 6: Log Analysis"
echo -e "${BLUE}Recent system logs (if available):${NC}"

if command -v journalctl &> /dev/null; then
    echo -e "${CYAN}Last 5 system log entries:${NC}"
    journalctl --no-pager -n 5 2>/dev/null || echo "System logs not accessible"
elif [[ -f "/var/log/syslog" ]]; then
    echo -e "${CYAN}Last 5 syslog entries:${NC}"
    tail -5 /var/log/syslog 2>/dev/null || echo "Syslog not accessible"
else
    echo -e "${YELLOW}⚠ System logs not available or accessible${NC}"
fi
echo ""
sleep 2

# Demo 7: File System Analysis
demo_section "Demo 7: File System Analysis"
echo -e "${BLUE}Largest directories in current path:${NC}"
du -h . 2>/dev/null | sort -rh | head -5 || echo "Directory analysis not available"
echo ""
echo -e "${BLUE}File count by type in current directory:${NC}"
find . -maxdepth 1 -type f | grep -E '\.[a-zA-Z0-9]+$' | sed 's/.*\.//' | sort | uniq -c | sort -rn | head -5 2>/dev/null || echo "File analysis not available"
echo ""
sleep 2

# Demo Summary
demo_section "Demo Complete!"
echo -e "${GREEN}This demo showed examples of:${NC}"
echo -e "${YELLOW}•${NC} System health monitoring"
echo -e "${YELLOW}•${NC} Build environment checking"
echo -e "${YELLOW}•${NC} Network connectivity testing"
echo -e "${YELLOW}•${NC} Docker status monitoring"
echo -e "${YELLOW}•${NC} Process monitoring"
echo -e "${YELLOW}•${NC} Log analysis"
echo -e "${YELLOW}•${NC} File system analysis"
echo ""
echo -e "${CYAN}To explore all features interactively, run:${NC}"
echo -e "${BLUE}./task-runner.sh${NC}"
echo ""
echo -e "${CYAN}For Azure operations, make sure you have Azure CLI installed and are logged in:${NC}"
echo -e "${BLUE}az login${NC}"
echo ""
echo -e "${GREEN}Happy DevOps automation! 🚀${NC}"
