#!/bin/bash

# DOCMS Task Runner - Modular Version
# Interactive DevOps Task Manager with modular architecture
# Author: Generated for DOCMS Tasks
# Version: 2.0

set -e

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MODULES_DIR="${SCRIPT_DIR}/modules"

# Check if modules directory exists
if [[ ! -d "$MODULES_DIR" ]]; then
    echo "Error: Modules directory not found at $MODULES_DIR"
    echo "Please ensure all module files are in the modules/ directory"
    exit 1
fi

# Load core utilities first
if [[ -f "${MODULES_DIR}/utils.sh" ]]; then
    source "${MODULES_DIR}/utils.sh"
    init_utils
else
    echo "Error: Core utilities module not found at ${MODULES_DIR}/utils.sh"
    exit 1
fi

# Load all module files
load_modules() {
    local modules=(
        "build.sh"
        "kubernetes.sh"
        "database.sh"
        "monitoring.sh"
        "testing.sh"
        "azure.sh"
    )
    
    for module in "${modules[@]}"; do
        local module_path="${MODULES_DIR}/${module}"
        if [[ -f "$module_path" ]]; then
            source "$module_path"
            success_message "Loaded module: $module"
        else
            warning_message "Module not found: $module"
        fi
    done
}

# View logs function
view_logs() {
    print_category_header "TASK RUNNER LOGS"
    
    if [[ ! -f "$LOG_FILE" ]]; then
        warning_message "No log file found. Logs will be created when tasks are executed."
        press_enter
        return
    fi
    
    info_message "Recent log entries:"
    echo ""
    tail -50 "$LOG_FILE"
    echo ""
    echo -e "${CYAN}Show full log? (y/N): ${NC}"
    read -r show_full
    
    if [[ "$show_full" =~ ^[yY]$ ]]; then
        if command -v less &> /dev/null; then
            less "$LOG_FILE"
        else
            cat "$LOG_FILE"
        fi
    fi
    
    press_enter
}

# Main menu
show_main_menu() {
    print_header
    echo -e "${GREEN}Select a category:${NC}"
    echo ""
    echo -e "${YELLOW}1.${NC} Build Operations"
    echo -e "${YELLOW}2.${NC} Kubernetes Operations"
    echo -e "${YELLOW}3.${NC} Database Operations"
    echo -e "${YELLOW}4.${NC} Monitoring Operations"
    echo -e "${YELLOW}5.${NC} Testing Operations"
    echo -e "${YELLOW}6.${NC} Azure Operations"
    echo -e "${YELLOW}7.${NC} View Logs"
    echo -e "${YELLOW}8.${NC} Module Status"
    echo -e "${YELLOW}0.${NC} Exit"
    echo ""
    echo -e "${CYAN}Enter your choice [0-8]: ${NC}"
}

# Module status check
module_status() {
    print_category_header "MODULE STATUS"
    
    local modules=(
        "utils.sh:Core Utilities"
        "build.sh:Build Operations"
        "kubernetes.sh:Kubernetes Operations"
        "database.sh:Database Operations"
        "monitoring.sh:Monitoring Operations"
        "testing.sh:Testing Operations"
        "azure.sh:Azure Operations"
    )
    
    info_message "Checking module availability:"
    echo ""
    
    for module_info in "${modules[@]}"; do
        IFS=':' read -r module_file module_name <<< "$module_info"
        local module_path="${MODULES_DIR}/${module_file}"
        
        echo -n -e "${CYAN}$module_name: ${NC}"
        if [[ -f "$module_path" ]]; then
            success_message "Available"
            
            # Check if main function exists for non-utils modules
            if [[ "$module_file" != "utils.sh" ]]; then
                local function_name="${module_file%.*}_menu"
                if declare -f "$function_name" > /dev/null; then
                    echo -e "  ${GREEN}✓ Menu function loaded${NC}"
                else
                    echo -e "  ${YELLOW}⚠ Menu function not found${NC}"
                fi
            fi
        else
            error_message "Missing"
        fi
        echo
    done
    
    echo
    info_message "Configuration:"
    echo -e "${CYAN}Script Directory:${NC} $SCRIPT_DIR"
    echo -e "${CYAN}Modules Directory:${NC} $MODULES_DIR"
    echo -e "${CYAN}Log File:${NC} $LOG_FILE"
    
    if [[ -f "${SCRIPT_DIR}/config.sh" ]]; then
        echo -e "${CYAN}Configuration File:${NC} ${GREEN}Found${NC}"
    else
        echo -e "${CYAN}Configuration File:${NC} ${YELLOW}Not found (optional)${NC}"
    fi
    
    press_enter
}

# Main execution loop
main() {
    # Load all modules
    info_message "Loading modules..."
    load_modules
    echo
    
    log "Modular Task Runner started"
    
    while true; do
        show_main_menu
        read -r choice
        
        case $choice in
            1) 
                if declare -f build_menu > /dev/null; then
                    build_menu
                else
                    error_message "Build module not available"
                    press_enter
                fi
                ;;
            2) 
                if declare -f kubernetes_menu > /dev/null; then
                    kubernetes_menu
                else
                    error_message "Kubernetes module not available"
                    press_enter
                fi
                ;;
            3) 
                if declare -f database_menu > /dev/null; then
                    database_menu
                else
                    error_message "Database module not available"
                    press_enter
                fi
                ;;
            4) 
                if declare -f monitoring_menu > /dev/null; then
                    monitoring_menu
                else
                    error_message "Monitoring module not available"
                    press_enter
                fi
                ;;
            5) 
                if declare -f testing_menu > /dev/null; then
                    testing_menu
                else
                    error_message "Testing module not available"
                    press_enter
                fi
                ;;
            6) 
                if declare -f azure_menu > /dev/null; then
                    azure_menu
                else
                    error_message "Azure module not available"
                    press_enter
                fi
                ;;
            7) view_logs ;;
            8) module_status ;;
            0) 
                success_message "Thank you for using DOCMS Task Runner!"
                log "Modular Task Runner exited"
                exit 0
                ;;
            *)
                error_message "Invalid option. Please try again."
                press_enter
                ;;
        esac
    done
}

# Help function
show_help() {
    echo "DOCMS Task Runner - Modular Version"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -v, --version  Show version information"
    echo "  -s, --status   Show module status and exit"
    echo ""
    echo "This script provides an interactive menu for common DevOps tasks."
    echo "All functionality is organized into modules for better maintainability."
    echo ""
    echo "Modules:"
    echo "  - Build Operations (Docker, Maven, NPM, Gradle)"
    echo "  - Kubernetes Operations (kubectl commands, deployments)"
    echo "  - Database Operations (PostgreSQL, MySQL, MongoDB)"
    echo "  - Monitoring Operations (System health, logs, performance)"
    echo "  - Testing Operations (Unit tests, integration tests, security)"
    echo "  - Azure Operations (Key Vault, VMs, storage, AKS)"
    echo ""
    echo "Configuration:"
    echo "  Create a config.sh file in the same directory for custom settings."
    echo "  See config.example.sh for available options."
}

# Version information
show_version() {
    echo "DOCMS Task Runner - Modular Version 2.0"
    echo "Interactive DevOps Task Manager"
    echo ""
    echo "Features:"
    echo "  - Modular architecture for better maintainability"
    echo "  - 6 main categories with 50+ operations"
    echo "  - Comprehensive logging and error handling"
    echo "  - Cross-platform compatibility (Linux/macOS)"
    echo ""
    echo "Author: Generated for DOCMS Tasks"
    echo "License: MIT"
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -v|--version)
        show_version
        exit 0
        ;;
    -s|--status)
        # Load utilities for status check
        if [[ -f "${MODULES_DIR}/utils.sh" ]]; then
            source "${MODULES_DIR}/utils.sh"
            init_utils
            load_modules
            module_status
        else
            echo "Error: Core utilities module not found"
            exit 1
        fi
        exit 0
        ;;
    "")
        # No arguments, run main program
        main "$@"
        ;;
    *)
        echo "Unknown option: $1"
        echo "Use -h or --help for usage information"
        exit 1
        ;;
esac
