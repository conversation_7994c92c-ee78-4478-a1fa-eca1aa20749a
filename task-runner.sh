#!/bin/bash

# Interactive Task Runner Script
# Provides organized categories for common DevOps tasks
# Author: Generated for DOCMS Tasks
# Version: 1.0

set -e

# Colors for better UX
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="${SCRIPT_DIR}/task-runner.log"

# Utility functions
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

print_header() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                    DOCMS Task Runner                         ║${NC}"
    echo -e "${BLUE}║              Interactive DevOps Task Manager                 ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

print_category_header() {
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                         $1${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

confirm_action() {
    echo -e "${YELLOW}Are you sure you want to proceed? (y/N): ${NC}"
    read -r confirmation
    case $confirmation in
        [yY]|[yY][eE][sS]) return 0 ;;
        *) echo -e "${RED}Operation cancelled.${NC}"; return 1 ;;
    esac
}

press_enter() {
    echo -e "${CYAN}Press Enter to continue...${NC}"
    read -r
}

# Main menu
show_main_menu() {
    print_header
    echo -e "${GREEN}Select a category:${NC}"
    echo ""
    echo -e "${YELLOW}1.${NC} Build Operations"
    echo -e "${YELLOW}2.${NC} Kubernetes Operations"
    echo -e "${YELLOW}3.${NC} Database Operations"
    echo -e "${YELLOW}4.${NC} Monitoring Operations"
    echo -e "${YELLOW}5.${NC} Testing Operations"
    echo -e "${YELLOW}6.${NC} Azure Operations"
    echo -e "${YELLOW}7.${NC} View Logs"
    echo -e "${YELLOW}0.${NC} Exit"
    echo ""
    echo -e "${CYAN}Enter your choice [0-7]: ${NC}"
}

# Category 1: Build Operations
build_menu() {
    while true; do
        print_category_header "BUILD OPERATIONS"
        echo -e "${GREEN}Available build tasks:${NC}"
        echo ""
        echo -e "${YELLOW}1.${NC} Docker Build & Push"
        echo -e "${YELLOW}2.${NC} Maven Build"
        echo -e "${YELLOW}3.${NC} NPM Build & Package"
        echo -e "${YELLOW}4.${NC} Gradle Build"
        echo -e "${YELLOW}5.${NC} Clean Build Artifacts"
        echo -e "${YELLOW}6.${NC} Build Status Check"
        echo -e "${YELLOW}0.${NC} Back to Main Menu"
        echo ""
        echo -e "${CYAN}Enter your choice [0-6]: ${NC}"
        
        read -r choice
        case $choice in
            1) docker_build_push ;;
            2) maven_build ;;
            3) npm_build ;;
            4) gradle_build ;;
            5) clean_artifacts ;;
            6) build_status ;;
            0) break ;;
            *) echo -e "${RED}Invalid option. Please try again.${NC}"; press_enter ;;
        esac
    done
}

docker_build_push() {
    echo -e "${GREEN}Docker Build & Push${NC}"
    echo -e "${CYAN}Enter Docker image name: ${NC}"
    read -r image_name
    echo -e "${CYAN}Enter tag (default: latest): ${NC}"
    read -r tag
    tag=${tag:-latest}
    
    if confirm_action; then
        log "Starting Docker build for $image_name:$tag"
        echo -e "${BLUE}Building Docker image...${NC}"
        docker build -t "$image_name:$tag" . && \
        echo -e "${BLUE}Pushing to registry...${NC}" && \
        docker push "$image_name:$tag" && \
        echo -e "${GREEN}✓ Docker build and push completed successfully${NC}" || \
        echo -e "${RED}✗ Docker build/push failed${NC}"
        log "Docker build completed for $image_name:$tag"
    fi
    press_enter
}

maven_build() {
    echo -e "${GREEN}Maven Build${NC}"
    echo -e "${CYAN}Select build type:${NC}"
    echo "1. Clean compile"
    echo "2. Package"
    echo "3. Install"
    echo "4. Test"
    read -r build_type
    
    case $build_type in
        1) mvn_cmd="clean compile" ;;
        2) mvn_cmd="clean package" ;;
        3) mvn_cmd="clean install" ;;
        4) mvn_cmd="test" ;;
        *) echo -e "${RED}Invalid option${NC}"; press_enter; return ;;
    esac
    
    if confirm_action; then
        log "Starting Maven build: $mvn_cmd"
        mvn $mvn_cmd && \
        echo -e "${GREEN}✓ Maven build completed successfully${NC}" || \
        echo -e "${RED}✗ Maven build failed${NC}"
    fi
    press_enter
}

npm_build() {
    echo -e "${GREEN}NPM Build & Package${NC}"
    echo -e "${CYAN}Select operation:${NC}"
    echo "1. Install dependencies"
    echo "2. Build"
    echo "3. Test"
    echo "4. Full pipeline (install + build + test)"
    read -r npm_choice
    
    case $npm_choice in
        1) npm_cmd="npm install" ;;
        2) npm_cmd="npm run build" ;;
        3) npm_cmd="npm test" ;;
        4) npm_cmd="npm install && npm run build && npm test" ;;
        *) echo -e "${RED}Invalid option${NC}"; press_enter; return ;;
    esac
    
    if confirm_action; then
        log "Starting NPM operation: $npm_cmd"
        eval $npm_cmd && \
        echo -e "${GREEN}✓ NPM operation completed successfully${NC}" || \
        echo -e "${RED}✗ NPM operation failed${NC}"
    fi
    press_enter
}

gradle_build() {
    echo -e "${GREEN}Gradle Build${NC}"
    echo -e "${CYAN}Select build task:${NC}"
    echo "1. Clean"
    echo "2. Build"
    echo "3. Test"
    echo "4. Assemble"
    read -r gradle_choice
    
    case $gradle_choice in
        1) gradle_cmd="clean" ;;
        2) gradle_cmd="build" ;;
        3) gradle_cmd="test" ;;
        4) gradle_cmd="assemble" ;;
        *) echo -e "${RED}Invalid option${NC}"; press_enter; return ;;
    esac
    
    if confirm_action; then
        log "Starting Gradle task: $gradle_cmd"
        ./gradlew $gradle_cmd && \
        echo -e "${GREEN}✓ Gradle task completed successfully${NC}" || \
        echo -e "${RED}✗ Gradle task failed${NC}"
    fi
    press_enter
}

# Category 2: Kubernetes Operations
kubernetes_menu() {
    while true; do
        print_category_header "KUBERNETES OPERATIONS"
        echo -e "${GREEN}Available Kubernetes tasks:${NC}"
        echo ""
        echo -e "${YELLOW}1.${NC} Get Cluster Info"
        echo -e "${YELLOW}2.${NC} Deploy Application"
        echo -e "${YELLOW}3.${NC} Scale Deployment"
        echo -e "${YELLOW}4.${NC} View Pods Status"
        echo -e "${YELLOW}5.${NC} View Logs"
        echo -e "${YELLOW}6.${NC} Port Forward"
        echo -e "${YELLOW}7.${NC} Apply Manifests"
        echo -e "${YELLOW}8.${NC} Delete Resources"
        echo -e "${YELLOW}0.${NC} Back to Main Menu"
        echo ""
        echo -e "${CYAN}Enter your choice [0-8]: ${NC}"

        read -r choice
        case $choice in
            1) k8s_cluster_info ;;
            2) k8s_deploy ;;
            3) k8s_scale ;;
            4) k8s_pods_status ;;
            5) k8s_logs ;;
            6) k8s_port_forward ;;
            7) k8s_apply_manifests ;;
            8) k8s_delete_resources ;;
            0) break ;;
            *) echo -e "${RED}Invalid option. Please try again.${NC}"; press_enter ;;
        esac
    done
}

k8s_cluster_info() {
    echo -e "${GREEN}Kubernetes Cluster Information${NC}"
    log "Fetching Kubernetes cluster info"

    echo -e "${BLUE}Cluster Info:${NC}"
    kubectl cluster-info 2>/dev/null || echo -e "${RED}✗ Unable to connect to cluster${NC}"

    echo -e "${BLUE}Nodes:${NC}"
    kubectl get nodes 2>/dev/null || echo -e "${RED}✗ Unable to fetch nodes${NC}"

    echo -e "${BLUE}Namespaces:${NC}"
    kubectl get namespaces 2>/dev/null || echo -e "${RED}✗ Unable to fetch namespaces${NC}"

    press_enter
}

k8s_deploy() {
    echo -e "${GREEN}Deploy Application${NC}"
    echo -e "${CYAN}Enter deployment name: ${NC}"
    read -r deployment_name
    echo -e "${CYAN}Enter image name: ${NC}"
    read -r image_name
    echo -e "${CYAN}Enter namespace (default: default): ${NC}"
    read -r namespace
    namespace=${namespace:-default}

    if confirm_action; then
        log "Deploying $deployment_name with image $image_name to namespace $namespace"
        kubectl create deployment "$deployment_name" --image="$image_name" -n "$namespace" && \
        echo -e "${GREEN}✓ Deployment created successfully${NC}" || \
        echo -e "${RED}✗ Deployment failed${NC}"
    fi
    press_enter
}

k8s_scale() {
    echo -e "${GREEN}Scale Deployment${NC}"
    echo -e "${CYAN}Enter deployment name: ${NC}"
    read -r deployment_name
    echo -e "${CYAN}Enter namespace (default: default): ${NC}"
    read -r namespace
    namespace=${namespace:-default}
    echo -e "${CYAN}Enter replica count: ${NC}"
    read -r replicas

    if confirm_action; then
        log "Scaling deployment $deployment_name to $replicas replicas"
        kubectl scale deployment "$deployment_name" --replicas="$replicas" -n "$namespace" && \
        echo -e "${GREEN}✓ Deployment scaled successfully${NC}" || \
        echo -e "${RED}✗ Scaling failed${NC}"
    fi
    press_enter
}

k8s_pods_status() {
    echo -e "${GREEN}Pods Status${NC}"
    echo -e "${CYAN}Enter namespace (default: all): ${NC}"
    read -r namespace

    if [[ -z "$namespace" || "$namespace" == "all" ]]; then
        kubectl get pods --all-namespaces
    else
        kubectl get pods -n "$namespace"
    fi
    press_enter
}

k8s_logs() {
    echo -e "${GREEN}View Pod Logs${NC}"
    echo -e "${CYAN}Enter pod name: ${NC}"
    read -r pod_name
    echo -e "${CYAN}Enter namespace (default: default): ${NC}"
    read -r namespace
    namespace=${namespace:-default}
    echo -e "${CYAN}Follow logs? (y/N): ${NC}"
    read -r follow

    if [[ "$follow" =~ ^[yY]$ ]]; then
        kubectl logs -f "$pod_name" -n "$namespace"
    else
        kubectl logs "$pod_name" -n "$namespace"
    fi
    press_enter
}

k8s_port_forward() {
    echo -e "${GREEN}Port Forward${NC}"
    echo -e "${CYAN}Enter pod/service name: ${NC}"
    read -r resource_name
    echo -e "${CYAN}Enter namespace (default: default): ${NC}"
    read -r namespace
    namespace=${namespace:-default}
    echo -e "${CYAN}Enter local port: ${NC}"
    read -r local_port
    echo -e "${CYAN}Enter remote port: ${NC}"
    read -r remote_port

    echo -e "${BLUE}Starting port forward... Press Ctrl+C to stop${NC}"
    kubectl port-forward "$resource_name" "$local_port:$remote_port" -n "$namespace"
    press_enter
}

k8s_apply_manifests() {
    echo -e "${GREEN}Apply Kubernetes Manifests${NC}"
    echo -e "${CYAN}Enter manifest file/directory path: ${NC}"
    read -r manifest_path

    if [[ ! -e "$manifest_path" ]]; then
        echo -e "${RED}✗ Path does not exist${NC}"
        press_enter
        return
    fi

    if confirm_action; then
        log "Applying manifests from $manifest_path"
        kubectl apply -f "$manifest_path" && \
        echo -e "${GREEN}✓ Manifests applied successfully${NC}" || \
        echo -e "${RED}✗ Failed to apply manifests${NC}"
    fi
    press_enter
}

k8s_delete_resources() {
    echo -e "${GREEN}Delete Kubernetes Resources${NC}"
    echo -e "${YELLOW}⚠️  WARNING: This will permanently delete resources${NC}"
    echo -e "${CYAN}Enter resource type (pod, deployment, service, etc.): ${NC}"
    read -r resource_type
    echo -e "${CYAN}Enter resource name: ${NC}"
    read -r resource_name
    echo -e "${CYAN}Enter namespace (default: default): ${NC}"
    read -r namespace
    namespace=${namespace:-default}

    if confirm_action; then
        log "Deleting $resource_type/$resource_name from namespace $namespace"
        kubectl delete "$resource_type" "$resource_name" -n "$namespace" && \
        echo -e "${GREEN}✓ Resource deleted successfully${NC}" || \
        echo -e "${RED}✗ Failed to delete resource${NC}"
    fi
    press_enter
}

# Category 3: Database Operations
database_menu() {
    while true; do
        print_category_header "DATABASE OPERATIONS"
        echo -e "${GREEN}Available database tasks:${NC}"
        echo ""
        echo -e "${YELLOW}1.${NC} PostgreSQL Operations"
        echo -e "${YELLOW}2.${NC} MySQL Operations"
        echo -e "${YELLOW}3.${NC} MongoDB Operations"
        echo -e "${YELLOW}4.${NC} Database Backup"
        echo -e "${YELLOW}5.${NC} Database Restore"
        echo -e "${YELLOW}6.${NC} Connection Test"
        echo -e "${YELLOW}7.${NC} Schema Migration"
        echo -e "${YELLOW}0.${NC} Back to Main Menu"
        echo ""
        echo -e "${CYAN}Enter your choice [0-7]: ${NC}"

        read -r choice
        case $choice in
            1) postgresql_operations ;;
            2) mysql_operations ;;
            3) mongodb_operations ;;
            4) database_backup ;;
            5) database_restore ;;
            6) database_connection_test ;;
            7) schema_migration ;;
            0) break ;;
            *) echo -e "${RED}Invalid option. Please try again.${NC}"; press_enter ;;
        esac
    done
}

postgresql_operations() {
    echo -e "${GREEN}PostgreSQL Operations${NC}"
    echo -e "${CYAN}Enter PostgreSQL host: ${NC}"
    read -r pg_host
    echo -e "${CYAN}Enter database name: ${NC}"
    read -r pg_database
    echo -e "${CYAN}Enter username: ${NC}"
    read -r pg_user

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. Connect and list tables"
    echo "2. Execute custom query"
    echo "3. Show database size"
    echo "4. Show active connections"
    read -r pg_choice

    case $pg_choice in
        1)
            psql -h "$pg_host" -d "$pg_database" -U "$pg_user" -c "\dt"
            ;;
        2)
            echo -e "${CYAN}Enter SQL query: ${NC}"
            read -r sql_query
            psql -h "$pg_host" -d "$pg_database" -U "$pg_user" -c "$sql_query"
            ;;
        3)
            psql -h "$pg_host" -d "$pg_database" -U "$pg_user" -c "SELECT pg_size_pretty(pg_database_size('$pg_database'));"
            ;;
        4)
            psql -h "$pg_host" -d "$pg_database" -U "$pg_user" -c "SELECT * FROM pg_stat_activity;"
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

mysql_operations() {
    echo -e "${GREEN}MySQL Operations${NC}"
    echo -e "${CYAN}Enter MySQL host: ${NC}"
    read -r mysql_host
    echo -e "${CYAN}Enter database name: ${NC}"
    read -r mysql_database
    echo -e "${CYAN}Enter username: ${NC}"
    read -r mysql_user

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. Show tables"
    echo "2. Execute custom query"
    echo "3. Show database size"
    echo "4. Show processlist"
    read -r mysql_choice

    case $mysql_choice in
        1)
            mysql -h "$mysql_host" -u "$mysql_user" -p "$mysql_database" -e "SHOW TABLES;"
            ;;
        2)
            echo -e "${CYAN}Enter SQL query: ${NC}"
            read -r sql_query
            mysql -h "$mysql_host" -u "$mysql_user" -p "$mysql_database" -e "$sql_query"
            ;;
        3)
            mysql -h "$mysql_host" -u "$mysql_user" -p -e "SELECT table_schema AS 'Database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' FROM information_schema.tables WHERE table_schema='$mysql_database';"
            ;;
        4)
            mysql -h "$mysql_host" -u "$mysql_user" -p -e "SHOW PROCESSLIST;"
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

mongodb_operations() {
    echo -e "${GREEN}MongoDB Operations${NC}"
    echo -e "${CYAN}Enter MongoDB connection string: ${NC}"
    read -r mongo_uri
    echo -e "${CYAN}Enter database name: ${NC}"
    read -r mongo_database

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. List collections"
    echo "2. Database stats"
    echo "3. Execute custom query"
    read -r mongo_choice

    case $mongo_choice in
        1)
            mongosh "$mongo_uri/$mongo_database" --eval "db.listCollections().forEach(printjson)"
            ;;
        2)
            mongosh "$mongo_uri/$mongo_database" --eval "db.stats()"
            ;;
        3)
            echo -e "${CYAN}Enter MongoDB query: ${NC}"
            read -r mongo_query
            mongosh "$mongo_uri/$mongo_database" --eval "$mongo_query"
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

database_backup() {
    echo -e "${GREEN}Database Backup${NC}"
    echo -e "${CYAN}Select database type:${NC}"
    echo "1. PostgreSQL"
    echo "2. MySQL"
    echo "3. MongoDB"
    read -r db_type

    case $db_type in
        1)
            echo -e "${CYAN}Enter PostgreSQL details:${NC}"
            read -p "Host: " pg_host
            read -p "Database: " pg_database
            read -p "Username: " pg_user
            read -p "Backup filename: " backup_file

            if confirm_action; then
                log "Creating PostgreSQL backup: $backup_file"
                pg_dump -h "$pg_host" -U "$pg_user" "$pg_database" > "$backup_file" && \
                echo -e "${GREEN}✓ Backup created: $backup_file${NC}" || \
                echo -e "${RED}✗ Backup failed${NC}"
            fi
            ;;
        2)
            echo -e "${CYAN}Enter MySQL details:${NC}"
            read -p "Host: " mysql_host
            read -p "Database: " mysql_database
            read -p "Username: " mysql_user
            read -p "Backup filename: " backup_file

            if confirm_action; then
                log "Creating MySQL backup: $backup_file"
                mysqldump -h "$mysql_host" -u "$mysql_user" -p "$mysql_database" > "$backup_file" && \
                echo -e "${GREEN}✓ Backup created: $backup_file${NC}" || \
                echo -e "${RED}✗ Backup failed${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter MongoDB details:${NC}"
            read -p "Connection URI: " mongo_uri
            read -p "Database: " mongo_database
            read -p "Backup directory: " backup_dir

            if confirm_action; then
                log "Creating MongoDB backup: $backup_dir"
                mongodump --uri="$mongo_uri" --db="$mongo_database" --out="$backup_dir" && \
                echo -e "${GREEN}✓ Backup created: $backup_dir${NC}" || \
                echo -e "${RED}✗ Backup failed${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

database_restore() {
    echo -e "${GREEN}Database Restore${NC}"
    echo -e "${YELLOW}⚠️  WARNING: This will overwrite existing data${NC}"
    echo -e "${CYAN}Select database type:${NC}"
    echo "1. PostgreSQL"
    echo "2. MySQL"
    echo "3. MongoDB"
    read -r db_type

    case $db_type in
        1)
            echo -e "${CYAN}Enter PostgreSQL details:${NC}"
            read -p "Host: " pg_host
            read -p "Database: " pg_database
            read -p "Username: " pg_user
            read -p "Backup filename: " backup_file

            if [[ ! -f "$backup_file" ]]; then
                echo -e "${RED}✗ Backup file not found${NC}"
                press_enter
                return
            fi

            if confirm_action; then
                log "Restoring PostgreSQL from: $backup_file"
                psql -h "$pg_host" -U "$pg_user" "$pg_database" < "$backup_file" && \
                echo -e "${GREEN}✓ Database restored successfully${NC}" || \
                echo -e "${RED}✗ Restore failed${NC}"
            fi
            ;;
        2)
            echo -e "${CYAN}Enter MySQL details:${NC}"
            read -p "Host: " mysql_host
            read -p "Database: " mysql_database
            read -p "Username: " mysql_user
            read -p "Backup filename: " backup_file

            if [[ ! -f "$backup_file" ]]; then
                echo -e "${RED}✗ Backup file not found${NC}"
                press_enter
                return
            fi

            if confirm_action; then
                log "Restoring MySQL from: $backup_file"
                mysql -h "$mysql_host" -u "$mysql_user" -p "$mysql_database" < "$backup_file" && \
                echo -e "${GREEN}✓ Database restored successfully${NC}" || \
                echo -e "${RED}✗ Restore failed${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter MongoDB details:${NC}"
            read -p "Connection URI: " mongo_uri
            read -p "Database: " mongo_database
            read -p "Backup directory: " backup_dir

            if [[ ! -d "$backup_dir" ]]; then
                echo -e "${RED}✗ Backup directory not found${NC}"
                press_enter
                return
            fi

            if confirm_action; then
                log "Restoring MongoDB from: $backup_dir"
                mongorestore --uri="$mongo_uri" --db="$mongo_database" "$backup_dir/$mongo_database" && \
                echo -e "${GREEN}✓ Database restored successfully${NC}" || \
                echo -e "${RED}✗ Restore failed${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

database_connection_test() {
    echo -e "${GREEN}Database Connection Test${NC}"
    echo -e "${CYAN}Select database type:${NC}"
    echo "1. PostgreSQL"
    echo "2. MySQL"
    echo "3. MongoDB"
    read -r db_type

    case $db_type in
        1)
            read -p "PostgreSQL Host: " pg_host
            read -p "Database: " pg_database
            read -p "Username: " pg_user

            echo -e "${BLUE}Testing PostgreSQL connection...${NC}"
            if psql -h "$pg_host" -U "$pg_user" -d "$pg_database" -c "SELECT 1;" >/dev/null 2>&1; then
                echo -e "${GREEN}✓ PostgreSQL connection successful${NC}"
            else
                echo -e "${RED}✗ PostgreSQL connection failed${NC}"
            fi
            ;;
        2)
            read -p "MySQL Host: " mysql_host
            read -p "Database: " mysql_database
            read -p "Username: " mysql_user

            echo -e "${BLUE}Testing MySQL connection...${NC}"
            if mysql -h "$mysql_host" -u "$mysql_user" -p"$mysql_password" -e "SELECT 1;" >/dev/null 2>&1; then
                echo -e "${GREEN}✓ MySQL connection successful${NC}"
            else
                echo -e "${RED}✗ MySQL connection failed${NC}"
            fi
            ;;
        3)
            read -p "MongoDB URI: " mongo_uri

            echo -e "${BLUE}Testing MongoDB connection...${NC}"
            if mongosh "$mongo_uri" --eval "db.runCommand({ping: 1})" >/dev/null 2>&1; then
                echo -e "${GREEN}✓ MongoDB connection successful${NC}"
            else
                echo -e "${RED}✗ MongoDB connection failed${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

schema_migration() {
    echo -e "${GREEN}Schema Migration${NC}"
    echo -e "${CYAN}Enter migration script path: ${NC}"
    read -r migration_path

    if [[ ! -f "$migration_path" ]]; then
        echo -e "${RED}✗ Migration script not found${NC}"
        press_enter
        return
    fi

    echo -e "${CYAN}Select database type:${NC}"
    echo "1. PostgreSQL"
    echo "2. MySQL"
    read -r db_type

    case $db_type in
        1)
            read -p "PostgreSQL Host: " pg_host
            read -p "Database: " pg_database
            read -p "Username: " pg_user

            if confirm_action; then
                log "Running PostgreSQL migration: $migration_path"
                psql -h "$pg_host" -U "$pg_user" -d "$pg_database" -f "$migration_path" && \
                echo -e "${GREEN}✓ Migration completed successfully${NC}" || \
                echo -e "${RED}✗ Migration failed${NC}"
            fi
            ;;
        2)
            read -p "MySQL Host: " mysql_host
            read -p "Database: " mysql_database
            read -p "Username: " mysql_user

            if confirm_action; then
                log "Running MySQL migration: $migration_path"
                mysql -h "$mysql_host" -u "$mysql_user" -p "$mysql_database" < "$migration_path" && \
                echo -e "${GREEN}✓ Migration completed successfully${NC}" || \
                echo -e "${RED}✗ Migration failed${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

# Category 4: Monitoring Operations
monitoring_menu() {
    while true; do
        print_category_header "MONITORING OPERATIONS"
        echo -e "${GREEN}Available monitoring tasks:${NC}"
        echo ""
        echo -e "${YELLOW}1.${NC} System Health Check"
        echo -e "${YELLOW}2.${NC} Docker Container Monitoring"
        echo -e "${YELLOW}3.${NC} Log Analysis"
        echo -e "${YELLOW}4.${NC} Network Monitoring"
        echo -e "${YELLOW}5.${NC} Disk Usage Check"
        echo -e "${YELLOW}6.${NC} Process Monitoring"
        echo -e "${YELLOW}7.${NC} Service Status Check"
        echo -e "${YELLOW}8.${NC} Performance Metrics"
        echo -e "${YELLOW}0.${NC} Back to Main Menu"
        echo ""
        echo -e "${CYAN}Enter your choice [0-8]: ${NC}"

        read -r choice
        case $choice in
            1) system_health_check ;;
            2) docker_monitoring ;;
            3) log_analysis ;;
            4) network_monitoring ;;
            5) disk_usage_check ;;
            6) process_monitoring ;;
            7) service_status_check ;;
            8) performance_metrics ;;
            0) break ;;
            *) echo -e "${RED}Invalid option. Please try again.${NC}"; press_enter ;;
        esac
    done
}

system_health_check() {
    echo -e "${GREEN}System Health Check${NC}"
    log "Running system health check"

    echo -e "${BLUE}System Information:${NC}"
    echo -e "${CYAN}Hostname:${NC} $(hostname)"
    echo -e "${CYAN}Uptime:${NC} $(uptime)"
    echo -e "${CYAN}Load Average:${NC} $(uptime | awk -F'load average:' '{print $2}')"

    echo -e "${BLUE}Memory Usage:${NC}"
    free -h

    echo -e "${BLUE}CPU Usage:${NC}"
    top -bn1 | grep "Cpu(s)" | awk '{print $2 $3 $4 $5 $6 $7 $8}'

    echo -e "${BLUE}Disk Usage:${NC}"
    df -h | grep -E '^/dev/'

    echo -e "${BLUE}Network Interfaces:${NC}"
    ip addr show | grep -E '^[0-9]+:|inet '

    press_enter
}

docker_monitoring() {
    echo -e "${GREEN}Docker Container Monitoring${NC}"

    if ! command -v docker &> /dev/null; then
        echo -e "${RED}✗ Docker is not installed or not in PATH${NC}"
        press_enter
        return
    fi

    echo -e "${BLUE}Running Containers:${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Image}}"

    echo -e "${BLUE}Container Resource Usage:${NC}"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

    echo -e "${BLUE}Docker System Info:${NC}"
    docker system df

    echo -e "${CYAN}Monitor specific container? (y/N): ${NC}"
    read -r monitor_specific

    if [[ "$monitor_specific" =~ ^[yY]$ ]]; then
        echo -e "${CYAN}Enter container name: ${NC}"
        read -r container_name
        echo -e "${BLUE}Real-time stats for $container_name (Press Ctrl+C to stop):${NC}"
        docker stats "$container_name"
    fi

    press_enter
}

log_analysis() {
    echo -e "${GREEN}Log Analysis${NC}"
    echo -e "${CYAN}Select log source:${NC}"
    echo "1. System logs (journalctl)"
    echo "2. Application logs"
    echo "3. Docker container logs"
    echo "4. Custom log file"
    read -r log_choice

    case $log_choice in
        1)
            echo -e "${CYAN}Select time range:${NC}"
            echo "1. Last hour"
            echo "2. Last 24 hours"
            echo "3. Last week"
            echo "4. Custom"
            read -r time_choice

            case $time_choice in
                1) journalctl --since "1 hour ago" ;;
                2) journalctl --since "24 hours ago" ;;
                3) journalctl --since "1 week ago" ;;
                4)
                    echo -e "${CYAN}Enter time range (e.g., '2023-01-01 00:00:00'): ${NC}"
                    read -r custom_time
                    journalctl --since "$custom_time"
                    ;;
                *) echo -e "${RED}Invalid option${NC}" ;;
            esac
            ;;
        2)
            echo -e "${CYAN}Enter application log directory: ${NC}"
            read -r log_dir
            if [[ -d "$log_dir" ]]; then
                echo -e "${BLUE}Recent log files in $log_dir:${NC}"
                find "$log_dir" -name "*.log" -mtime -1 -exec ls -la {} \;
                echo -e "${CYAN}Enter specific log file to analyze: ${NC}"
                read -r log_file
                if [[ -f "$log_file" ]]; then
                    tail -100 "$log_file"
                else
                    echo -e "${RED}✗ Log file not found${NC}"
                fi
            else
                echo -e "${RED}✗ Directory not found${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter container name: ${NC}"
            read -r container_name
            echo -e "${CYAN}Number of lines to show (default: 100): ${NC}"
            read -r lines
            lines=${lines:-100}
            docker logs --tail "$lines" "$container_name"
            ;;
        4)
            echo -e "${CYAN}Enter log file path: ${NC}"
            read -r log_file
            if [[ -f "$log_file" ]]; then
                echo -e "${CYAN}Analysis options:${NC}"
                echo "1. Show last 100 lines"
                echo "2. Search for pattern"
                echo "3. Show error lines"
                echo "4. Show file statistics"
                read -r analysis_choice

                case $analysis_choice in
                    1) tail -100 "$log_file" ;;
                    2)
                        echo -e "${CYAN}Enter search pattern: ${NC}"
                        read -r pattern
                        grep -n "$pattern" "$log_file"
                        ;;
                    3) grep -i "error\|exception\|fail" "$log_file" ;;
                    4)
                        echo -e "${BLUE}File: $log_file${NC}"
                        echo -e "${CYAN}Size:${NC} $(du -h "$log_file" | cut -f1)"
                        echo -e "${CYAN}Lines:${NC} $(wc -l < "$log_file")"
                        echo -e "${CYAN}Last modified:${NC} $(stat -c %y "$log_file")"
                        ;;
                    *) echo -e "${RED}Invalid option${NC}" ;;
                esac
            else
                echo -e "${RED}✗ Log file not found${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

network_monitoring() {
    echo -e "${GREEN}Network Monitoring${NC}"

    echo -e "${BLUE}Network Interfaces:${NC}"
    ip addr show

    echo -e "${BLUE}Network Statistics:${NC}"
    netstat -i

    echo -e "${BLUE}Active Connections:${NC}"
    netstat -tuln | head -20

    echo -e "${CYAN}Test connectivity to specific host? (y/N): ${NC}"
    read -r test_connectivity

    if [[ "$test_connectivity" =~ ^[yY]$ ]]; then
        echo -e "${CYAN}Enter hostname or IP: ${NC}"
        read -r target_host
        echo -e "${CYAN}Enter port (optional): ${NC}"
        read -r target_port

        echo -e "${BLUE}Ping test:${NC}"
        ping -c 4 "$target_host"

        if [[ -n "$target_port" ]]; then
            echo -e "${BLUE}Port connectivity test:${NC}"
            nc -zv "$target_host" "$target_port" 2>&1
        fi
    fi

    press_enter
}

disk_usage_check() {
    echo -e "${GREEN}Disk Usage Check${NC}"

    echo -e "${BLUE}Filesystem Usage:${NC}"
    df -h

    echo -e "${BLUE}Inode Usage:${NC}"
    df -i

    echo -e "${BLUE}Largest Directories (Top 10):${NC}"
    du -h / 2>/dev/null | sort -rh | head -10

    echo -e "${CYAN}Check specific directory? (y/N): ${NC}"
    read -r check_specific

    if [[ "$check_specific" =~ ^[yY]$ ]]; then
        echo -e "${CYAN}Enter directory path: ${NC}"
        read -r dir_path
        if [[ -d "$dir_path" ]]; then
            echo -e "${BLUE}Directory usage for $dir_path:${NC}"
            du -h "$dir_path" | sort -rh | head -20
        else
            echo -e "${RED}✗ Directory not found${NC}"
        fi
    fi

    press_enter
}

process_monitoring() {
    echo -e "${GREEN}Process Monitoring${NC}"

    echo -e "${BLUE}Top Processes by CPU:${NC}"
    ps aux --sort=-%cpu | head -10

    echo -e "${BLUE}Top Processes by Memory:${NC}"
    ps aux --sort=-%mem | head -10

    echo -e "${BLUE}Process Tree:${NC}"
    pstree -p | head -20

    echo -e "${CYAN}Monitor specific process? (y/N): ${NC}"
    read -r monitor_process

    if [[ "$monitor_process" =~ ^[yY]$ ]]; then
        echo -e "${CYAN}Enter process name or PID: ${NC}"
        read -r process_name

        if [[ "$process_name" =~ ^[0-9]+$ ]]; then
            # It's a PID
            ps -p "$process_name" -o pid,ppid,cmd,%cpu,%mem,etime
        else
            # It's a process name
            pgrep -f "$process_name" | xargs -I {} ps -p {} -o pid,ppid,cmd,%cpu,%mem,etime
        fi
    fi

    press_enter
}

service_status_check() {
    echo -e "${GREEN}Service Status Check${NC}"

    echo -e "${BLUE}Systemd Services Status:${NC}"
    systemctl list-units --type=service --state=running | head -20

    echo -e "${BLUE}Failed Services:${NC}"
    systemctl list-units --type=service --state=failed

    echo -e "${CYAN}Check specific service? (y/N): ${NC}"
    read -r check_service

    if [[ "$check_service" =~ ^[yY]$ ]]; then
        echo -e "${CYAN}Enter service name: ${NC}"
        read -r service_name

        echo -e "${BLUE}Service Status:${NC}"
        systemctl status "$service_name"

        echo -e "${CYAN}Show service logs? (y/N): ${NC}"
        read -r show_logs

        if [[ "$show_logs" =~ ^[yY]$ ]]; then
            journalctl -u "$service_name" --since "1 hour ago"
        fi
    fi

    press_enter
}

performance_metrics() {
    echo -e "${GREEN}Performance Metrics${NC}"

    echo -e "${BLUE}System Load:${NC}"
    uptime

    echo -e "${BLUE}CPU Information:${NC}"
    lscpu | grep -E "Model name|CPU\(s\)|Thread|Core"

    echo -e "${BLUE}Memory Information:${NC}"
    free -h
    cat /proc/meminfo | grep -E "MemTotal|MemFree|MemAvailable|Cached|Buffers"

    echo -e "${BLUE}I/O Statistics:${NC}"
    if command -v iostat &> /dev/null; then
        iostat -x 1 1
    else
        echo -e "${YELLOW}iostat not available. Install sysstat package for detailed I/O stats.${NC}"
        cat /proc/diskstats | head -5
    fi

    echo -e "${CYAN}Run continuous monitoring? (y/N): ${NC}"
    read -r continuous_monitor

    if [[ "$continuous_monitor" =~ ^[yY]$ ]]; then
        echo -e "${BLUE}Starting continuous monitoring (Press Ctrl+C to stop):${NC}"
        if command -v htop &> /dev/null; then
            htop
        else
            top
        fi
    fi

    press_enter
}

# Category 5: Testing Operations
testing_menu() {
    while true; do
        print_category_header "TESTING OPERATIONS"
        echo -e "${GREEN}Available testing tasks:${NC}"
        echo ""
        echo -e "${YELLOW}1.${NC} Unit Tests"
        echo -e "${YELLOW}2.${NC} Integration Tests"
        echo -e "${YELLOW}3.${NC} Load Testing"
        echo -e "${YELLOW}4.${NC} API Testing"
        echo -e "${YELLOW}5.${NC} Security Testing"
        echo -e "${YELLOW}6.${NC} Code Quality Check"
        echo -e "${YELLOW}7.${NC} Test Coverage Report"
        echo -e "${YELLOW}8.${NC} Performance Testing"
        echo -e "${YELLOW}0.${NC} Back to Main Menu"
        echo ""
        echo -e "${CYAN}Enter your choice [0-8]: ${NC}"

        read -r choice
        case $choice in
            1) unit_tests ;;
            2) integration_tests ;;
            3) load_testing ;;
            4) api_testing ;;
            5) security_testing ;;
            6) code_quality_check ;;
            7) test_coverage_report ;;
            8) performance_testing ;;
            0) break ;;
            *) echo -e "${RED}Invalid option. Please try again.${NC}"; press_enter ;;
        esac
    done
}

unit_tests() {
    echo -e "${GREEN}Unit Tests${NC}"
    echo -e "${CYAN}Select testing framework:${NC}"
    echo "1. Maven (Java)"
    echo "2. NPM/Jest (JavaScript)"
    echo "3. pytest (Python)"
    echo "4. Go test (Go)"
    echo "5. Cargo test (Rust)"
    echo "6. Custom command"
    read -r test_framework

    case $test_framework in
        1)
            echo -e "${BLUE}Running Maven tests...${NC}"
            if confirm_action; then
                log "Running Maven unit tests"
                mvn test && \
                echo -e "${GREEN}✓ Maven tests completed${NC}" || \
                echo -e "${RED}✗ Maven tests failed${NC}"
            fi
            ;;
        2)
            echo -e "${BLUE}Running NPM/Jest tests...${NC}"
            if confirm_action; then
                log "Running NPM unit tests"
                npm test && \
                echo -e "${GREEN}✓ NPM tests completed${NC}" || \
                echo -e "${RED}✗ NPM tests failed${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter test directory (default: tests/): ${NC}"
            read -r test_dir
            test_dir=${test_dir:-tests/}

            if confirm_action; then
                log "Running pytest unit tests"
                pytest "$test_dir" -v && \
                echo -e "${GREEN}✓ pytest tests completed${NC}" || \
                echo -e "${RED}✗ pytest tests failed${NC}"
            fi
            ;;
        4)
            echo -e "${BLUE}Running Go tests...${NC}"
            if confirm_action; then
                log "Running Go unit tests"
                go test ./... -v && \
                echo -e "${GREEN}✓ Go tests completed${NC}" || \
                echo -e "${RED}✗ Go tests failed${NC}"
            fi
            ;;
        5)
            echo -e "${BLUE}Running Cargo tests...${NC}"
            if confirm_action; then
                log "Running Rust unit tests"
                cargo test && \
                echo -e "${GREEN}✓ Cargo tests completed${NC}" || \
                echo -e "${RED}✗ Cargo tests failed${NC}"
            fi
            ;;
        6)
            echo -e "${CYAN}Enter custom test command: ${NC}"
            read -r custom_command
            if confirm_action; then
                log "Running custom test command: $custom_command"
                eval "$custom_command" && \
                echo -e "${GREEN}✓ Custom tests completed${NC}" || \
                echo -e "${RED}✗ Custom tests failed${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

integration_tests() {
    echo -e "${GREEN}Integration Tests${NC}"
    echo -e "${CYAN}Select integration test type:${NC}"
    echo "1. Database integration tests"
    echo "2. API integration tests"
    echo "3. Service integration tests"
    echo "4. End-to-end tests"
    echo "5. Custom integration tests"
    read -r integration_type

    case $integration_type in
        1)
            echo -e "${BLUE}Database Integration Tests${NC}"
            echo -e "${CYAN}Enter test command (e.g., mvn test -Dtest=*IntegrationTest): ${NC}"
            read -r db_test_command
            if confirm_action; then
                log "Running database integration tests"
                eval "$db_test_command" && \
                echo -e "${GREEN}✓ Database integration tests completed${NC}" || \
                echo -e "${RED}✗ Database integration tests failed${NC}"
            fi
            ;;
        2)
            echo -e "${BLUE}API Integration Tests${NC}"
            echo -e "${CYAN}Enter API base URL: ${NC}"
            read -r api_url
            echo -e "${CYAN}Enter test script path: ${NC}"
            read -r test_script

            if [[ -f "$test_script" ]]; then
                if confirm_action; then
                    log "Running API integration tests against $api_url"
                    API_URL="$api_url" bash "$test_script" && \
                    echo -e "${GREEN}✓ API integration tests completed${NC}" || \
                    echo -e "${RED}✗ API integration tests failed${NC}"
                fi
            else
                echo -e "${RED}✗ Test script not found${NC}"
            fi
            ;;
        3)
            echo -e "${BLUE}Service Integration Tests${NC}"
            echo -e "${CYAN}Enter docker-compose file path (optional): ${NC}"
            read -r compose_file

            if [[ -n "$compose_file" && -f "$compose_file" ]]; then
                echo -e "${BLUE}Starting services with docker-compose...${NC}"
                docker-compose -f "$compose_file" up -d
                sleep 10
            fi

            echo -e "${CYAN}Enter test command: ${NC}"
            read -r service_test_command

            if confirm_action; then
                log "Running service integration tests"
                eval "$service_test_command" && \
                echo -e "${GREEN}✓ Service integration tests completed${NC}" || \
                echo -e "${RED}✗ Service integration tests failed${NC}"
            fi

            if [[ -n "$compose_file" && -f "$compose_file" ]]; then
                echo -e "${BLUE}Stopping services...${NC}"
                docker-compose -f "$compose_file" down
            fi
            ;;
        4)
            echo -e "${BLUE}End-to-End Tests${NC}"
            echo -e "${CYAN}Select E2E framework:${NC}"
            echo "1. Selenium"
            echo "2. Cypress"
            echo "3. Playwright"
            echo "4. Custom"
            read -r e2e_framework

            case $e2e_framework in
                1) e2e_command="mvn test -Dtest=*E2ETest" ;;
                2) e2e_command="npx cypress run" ;;
                3) e2e_command="npx playwright test" ;;
                4)
                    echo -e "${CYAN}Enter custom E2E command: ${NC}"
                    read -r e2e_command
                    ;;
                *) echo -e "${RED}Invalid option${NC}"; press_enter; return ;;
            esac

            if confirm_action; then
                log "Running E2E tests with $e2e_framework"
                eval "$e2e_command" && \
                echo -e "${GREEN}✓ E2E tests completed${NC}" || \
                echo -e "${RED}✗ E2E tests failed${NC}"
            fi
            ;;
        5)
            echo -e "${CYAN}Enter custom integration test command: ${NC}"
            read -r custom_integration_command
            if confirm_action; then
                log "Running custom integration tests"
                eval "$custom_integration_command" && \
                echo -e "${GREEN}✓ Custom integration tests completed${NC}" || \
                echo -e "${RED}✗ Custom integration tests failed${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

load_testing() {
    echo -e "${GREEN}Load Testing${NC}"
    echo -e "${CYAN}Select load testing tool:${NC}"
    echo "1. Apache Bench (ab)"
    echo "2. curl (simple load test)"
    echo "3. JMeter"
    echo "4. Artillery"
    echo "5. Custom load test"
    read -r load_tool

    case $load_tool in
        1)
            echo -e "${CYAN}Enter target URL: ${NC}"
            read -r target_url
            echo -e "${CYAN}Enter number of requests (default: 100): ${NC}"
            read -r num_requests
            num_requests=${num_requests:-100}
            echo -e "${CYAN}Enter concurrency level (default: 10): ${NC}"
            read -r concurrency
            concurrency=${concurrency:-10}

            if confirm_action; then
                log "Running Apache Bench load test on $target_url"
                ab -n "$num_requests" -c "$concurrency" "$target_url" && \
                echo -e "${GREEN}✓ Load test completed${NC}" || \
                echo -e "${RED}✗ Load test failed${NC}"
            fi
            ;;
        2)
            echo -e "${CYAN}Enter target URL: ${NC}"
            read -r target_url
            echo -e "${CYAN}Enter number of parallel requests (default: 10): ${NC}"
            read -r parallel_requests
            parallel_requests=${parallel_requests:-10}

            if confirm_action; then
                log "Running curl load test on $target_url"
                for i in $(seq 1 "$parallel_requests"); do
                    curl -s -o /dev/null -w "%{http_code} %{time_total}s\n" "$target_url" &
                done
                wait
                echo -e "${GREEN}✓ Curl load test completed${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter JMeter test plan file: ${NC}"
            read -r jmeter_plan

            if [[ ! -f "$jmeter_plan" ]]; then
                echo -e "${RED}✗ JMeter test plan not found${NC}"
                press_enter
                return
            fi

            if confirm_action; then
                log "Running JMeter load test with plan $jmeter_plan"
                jmeter -n -t "$jmeter_plan" -l results.jtl && \
                echo -e "${GREEN}✓ JMeter load test completed${NC}" || \
                echo -e "${RED}✗ JMeter load test failed${NC}"
            fi
            ;;
        4)
            echo -e "${CYAN}Enter Artillery config file: ${NC}"
            read -r artillery_config

            if [[ ! -f "$artillery_config" ]]; then
                echo -e "${RED}✗ Artillery config not found${NC}"
                press_enter
                return
            fi

            if confirm_action; then
                log "Running Artillery load test with config $artillery_config"
                artillery run "$artillery_config" && \
                echo -e "${GREEN}✓ Artillery load test completed${NC}" || \
                echo -e "${RED}✗ Artillery load test failed${NC}"
            fi
            ;;
        5)
            echo -e "${CYAN}Enter custom load test command: ${NC}"
            read -r custom_load_command
            if confirm_action; then
                log "Running custom load test"
                eval "$custom_load_command" && \
                echo -e "${GREEN}✓ Custom load test completed${NC}" || \
                echo -e "${RED}✗ Custom load test failed${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

api_testing() {
    echo -e "${GREEN}API Testing${NC}"
    echo -e "${CYAN}Enter API base URL: ${NC}"
    read -r api_base_url

    echo -e "${CYAN}Select API test type:${NC}"
    echo "1. Health check"
    echo "2. Authentication test"
    echo "3. CRUD operations test"
    echo "4. Custom API test"
    read -r api_test_type

    case $api_test_type in
        1)
            echo -e "${BLUE}Testing API health check...${NC}"
            response=$(curl -s -o /dev/null -w "%{http_code}" "$api_base_url/health" || echo "000")
            if [[ "$response" == "200" ]]; then
                echo -e "${GREEN}✓ API health check passed${NC}"
            else
                echo -e "${RED}✗ API health check failed (HTTP $response)${NC}"
            fi
            ;;
        2)
            echo -e "${CYAN}Enter authentication endpoint: ${NC}"
            read -r auth_endpoint
            echo -e "${CYAN}Enter username: ${NC}"
            read -r username
            echo -e "${CYAN}Enter password: ${NC}"
            read -s password
            echo

            response=$(curl -s -X POST "$api_base_url$auth_endpoint" \
                -H "Content-Type: application/json" \
                -d "{\"username\":\"$username\",\"password\":\"$password\"}" \
                -w "%{http_code}")

            if [[ "$response" =~ 200|201 ]]; then
                echo -e "${GREEN}✓ Authentication test passed${NC}"
            else
                echo -e "${RED}✗ Authentication test failed${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter resource endpoint (e.g., /users): ${NC}"
            read -r resource_endpoint

            echo -e "${BLUE}Testing GET request...${NC}"
            get_response=$(curl -s -o /dev/null -w "%{http_code}" "$api_base_url$resource_endpoint")
            echo "GET: HTTP $get_response"

            echo -e "${BLUE}Testing POST request...${NC}"
            post_response=$(curl -s -X POST "$api_base_url$resource_endpoint" \
                -H "Content-Type: application/json" \
                -d '{"test":"data"}' \
                -o /dev/null -w "%{http_code}")
            echo "POST: HTTP $post_response"
            ;;
        4)
            echo -e "${CYAN}Enter custom curl command: ${NC}"
            read -r custom_curl
            eval "$custom_curl"
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

security_testing() {
    echo -e "${GREEN}Security Testing${NC}"
    echo -e "${CYAN}Select security test type:${NC}"
    echo "1. SSL/TLS Certificate Check"
    echo "2. Port Scan"
    echo "3. Dependency Vulnerability Scan"
    echo "4. Docker Image Security Scan"
    echo "5. Custom Security Test"
    read -r security_test_type

    case $security_test_type in
        1)
            echo -e "${CYAN}Enter hostname: ${NC}"
            read -r hostname
            echo -e "${BLUE}Checking SSL/TLS certificate...${NC}"

            openssl s_client -connect "$hostname:443" -servername "$hostname" < /dev/null 2>/dev/null | \
            openssl x509 -noout -dates -subject -issuer
            ;;
        2)
            echo -e "${CYAN}Enter target host: ${NC}"
            read -r target_host
            echo -e "${CYAN}Enter port range (e.g., 1-1000): ${NC}"
            read -r port_range

            if command -v nmap &> /dev/null; then
                echo -e "${BLUE}Running nmap scan...${NC}"
                nmap -p "$port_range" "$target_host"
            else
                echo -e "${YELLOW}nmap not available. Using basic port check...${NC}"
                IFS='-' read -r start_port end_port <<< "$port_range"
                for port in $(seq "$start_port" "$end_port"); do
                    if nc -z "$target_host" "$port" 2>/dev/null; then
                        echo "Port $port: Open"
                    fi
                done
            fi
            ;;
        3)
            echo -e "${CYAN}Select dependency scanner:${NC}"
            echo "1. npm audit (Node.js)"
            echo "2. pip-audit (Python)"
            echo "3. cargo audit (Rust)"
            echo "4. OWASP Dependency Check"
            read -r dep_scanner

            case $dep_scanner in
                1) npm audit ;;
                2) pip-audit ;;
                3) cargo audit ;;
                4)
                    echo -e "${CYAN}Enter project directory: ${NC}"
                    read -r project_dir
                    dependency-check --project "Security Scan" --scan "$project_dir"
                    ;;
                *) echo -e "${RED}Invalid option${NC}" ;;
            esac
            ;;
        4)
            echo -e "${CYAN}Enter Docker image name: ${NC}"
            read -r docker_image

            if command -v trivy &> /dev/null; then
                echo -e "${BLUE}Running Trivy security scan...${NC}"
                trivy image "$docker_image"
            else
                echo -e "${YELLOW}Trivy not available. Install Trivy for Docker image security scanning.${NC}"
                echo -e "${BLUE}Running basic Docker image inspection...${NC}"
                docker inspect "$docker_image" | jq '.[0].Config'
            fi
            ;;
        5)
            echo -e "${CYAN}Enter custom security test command: ${NC}"
            read -r custom_security_command
            eval "$custom_security_command"
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

code_quality_check() {
    echo -e "${GREEN}Code Quality Check${NC}"
    echo -e "${CYAN}Select code quality tool:${NC}"
    echo "1. SonarQube scan"
    echo "2. ESLint (JavaScript)"
    echo "3. Pylint (Python)"
    echo "4. Checkstyle (Java)"
    echo "5. Clippy (Rust)"
    echo "6. Custom quality check"
    read -r quality_tool

    case $quality_tool in
        1)
            echo -e "${CYAN}Enter SonarQube server URL: ${NC}"
            read -r sonar_url
            echo -e "${CYAN}Enter project key: ${NC}"
            read -r project_key

            if confirm_action; then
                log "Running SonarQube scan for project $project_key"
                sonar-scanner \
                    -Dsonar.projectKey="$project_key" \
                    -Dsonar.host.url="$sonar_url" && \
                echo -e "${GREEN}✓ SonarQube scan completed${NC}" || \
                echo -e "${RED}✗ SonarQube scan failed${NC}"
            fi
            ;;
        2)
            echo -e "${BLUE}Running ESLint...${NC}"
            npx eslint . --ext .js,.jsx,.ts,.tsx && \
            echo -e "${GREEN}✓ ESLint check passed${NC}" || \
            echo -e "${RED}✗ ESLint check failed${NC}"
            ;;
        3)
            echo -e "${CYAN}Enter Python source directory: ${NC}"
            read -r python_dir
            echo -e "${BLUE}Running Pylint...${NC}"
            pylint "$python_dir" && \
            echo -e "${GREEN}✓ Pylint check passed${NC}" || \
            echo -e "${RED}✗ Pylint check failed${NC}"
            ;;
        4)
            echo -e "${BLUE}Running Checkstyle...${NC}"
            mvn checkstyle:check && \
            echo -e "${GREEN}✓ Checkstyle check passed${NC}" || \
            echo -e "${RED}✗ Checkstyle check failed${NC}"
            ;;
        5)
            echo -e "${BLUE}Running Clippy...${NC}"
            cargo clippy -- -D warnings && \
            echo -e "${GREEN}✓ Clippy check passed${NC}" || \
            echo -e "${RED}✗ Clippy check failed${NC}"
            ;;
        6)
            echo -e "${CYAN}Enter custom quality check command: ${NC}"
            read -r custom_quality_command
            eval "$custom_quality_command" && \
            echo -e "${GREEN}✓ Custom quality check passed${NC}" || \
            echo -e "${RED}✗ Custom quality check failed${NC}"
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

test_coverage_report() {
    echo -e "${GREEN}Test Coverage Report${NC}"
    echo -e "${CYAN}Select coverage tool:${NC}"
    echo "1. JaCoCo (Java)"
    echo "2. Jest coverage (JavaScript)"
    echo "3. pytest-cov (Python)"
    echo "4. gocov (Go)"
    echo "5. tarpaulin (Rust)"
    echo "6. Custom coverage tool"
    read -r coverage_tool

    case $coverage_tool in
        1)
            echo -e "${BLUE}Generating JaCoCo coverage report...${NC}"
            mvn jacoco:report && \
            echo -e "${GREEN}✓ JaCoCo report generated${NC}" && \
            echo -e "${CYAN}Report location: target/site/jacoco/index.html${NC}" || \
            echo -e "${RED}✗ JaCoCo report generation failed${NC}"
            ;;
        2)
            echo -e "${BLUE}Generating Jest coverage report...${NC}"
            npm test -- --coverage && \
            echo -e "${GREEN}✓ Jest coverage report generated${NC}" && \
            echo -e "${CYAN}Report location: coverage/lcov-report/index.html${NC}" || \
            echo -e "${RED}✗ Jest coverage report generation failed${NC}"
            ;;
        3)
            echo -e "${CYAN}Enter test directory: ${NC}"
            read -r test_dir
            echo -e "${BLUE}Generating pytest coverage report...${NC}"
            pytest "$test_dir" --cov=. --cov-report=html && \
            echo -e "${GREEN}✓ pytest coverage report generated${NC}" && \
            echo -e "${CYAN}Report location: htmlcov/index.html${NC}" || \
            echo -e "${RED}✗ pytest coverage report generation failed${NC}"
            ;;
        4)
            echo -e "${BLUE}Generating Go coverage report...${NC}"
            go test -coverprofile=coverage.out ./... && \
            go tool cover -html=coverage.out -o coverage.html && \
            echo -e "${GREEN}✓ Go coverage report generated${NC}" && \
            echo -e "${CYAN}Report location: coverage.html${NC}" || \
            echo -e "${RED}✗ Go coverage report generation failed${NC}"
            ;;
        5)
            echo -e "${BLUE}Generating Rust coverage report...${NC}"
            cargo tarpaulin --out Html && \
            echo -e "${GREEN}✓ Rust coverage report generated${NC}" && \
            echo -e "${CYAN}Report location: tarpaulin-report.html${NC}" || \
            echo -e "${RED}✗ Rust coverage report generation failed${NC}"
            ;;
        6)
            echo -e "${CYAN}Enter custom coverage command: ${NC}"
            read -r custom_coverage_command
            eval "$custom_coverage_command" && \
            echo -e "${GREEN}✓ Custom coverage report generated${NC}" || \
            echo -e "${RED}✗ Custom coverage report generation failed${NC}"
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

performance_testing() {
    echo -e "${GREEN}Performance Testing${NC}"
    echo -e "${CYAN}Select performance test type:${NC}"
    echo "1. Application startup time"
    echo "2. Memory usage profiling"
    echo "3. CPU usage profiling"
    echo "4. Database query performance"
    echo "5. Custom performance test"
    read -r perf_test_type

    case $perf_test_type in
        1)
            echo -e "${CYAN}Enter application start command: ${NC}"
            read -r start_command
            echo -e "${BLUE}Measuring startup time...${NC}"

            start_time=$(date +%s.%N)
            eval "$start_command" &
            app_pid=$!

            # Wait for application to be ready (customize this check)
            sleep 5
            end_time=$(date +%s.%N)

            startup_time=$(echo "$end_time - $start_time" | bc)
            echo -e "${GREEN}Startup time: ${startup_time}s${NC}"

            kill $app_pid 2>/dev/null || true
            ;;
        2)
            echo -e "${CYAN}Enter process name or PID: ${NC}"
            read -r process_name
            echo -e "${BLUE}Monitoring memory usage for 60 seconds...${NC}"

            if [[ "$process_name" =~ ^[0-9]+$ ]]; then
                # It's a PID
                for i in {1..60}; do
                    ps -p "$process_name" -o %mem,rss | tail -1
                    sleep 1
                done
            else
                # It's a process name
                for i in {1..60}; do
                    pgrep "$process_name" | xargs -I {} ps -p {} -o %mem,rss | tail -1
                    sleep 1
                done
            fi
            ;;
        3)
            echo -e "${CYAN}Enter process name or PID: ${NC}"
            read -r process_name
            echo -e "${BLUE}Monitoring CPU usage for 60 seconds...${NC}"

            if [[ "$process_name" =~ ^[0-9]+$ ]]; then
                # It's a PID
                for i in {1..60}; do
                    ps -p "$process_name" -o %cpu | tail -1
                    sleep 1
                done
            else
                # It's a process name
                for i in {1..60}; do
                    pgrep "$process_name" | xargs -I {} ps -p {} -o %cpu | tail -1
                    sleep 1
                done
            fi
            ;;
        4)
            echo -e "${CYAN}Select database type:${NC}"
            echo "1. PostgreSQL"
            echo "2. MySQL"
            read -r db_type

            echo -e "${CYAN}Enter test query: ${NC}"
            read -r test_query

            case $db_type in
                1)
                    read -p "PostgreSQL Host: " pg_host
                    read -p "Database: " pg_database
                    read -p "Username: " pg_user

                    echo -e "${BLUE}Running query performance test...${NC}"
                    psql -h "$pg_host" -U "$pg_user" -d "$pg_database" -c "EXPLAIN ANALYZE $test_query"
                    ;;
                2)
                    read -p "MySQL Host: " mysql_host
                    read -p "Database: " mysql_database
                    read -p "Username: " mysql_user

                    echo -e "${BLUE}Running query performance test...${NC}"
                    mysql -h "$mysql_host" -u "$mysql_user" -p "$mysql_database" -e "EXPLAIN $test_query"
                    ;;
                *)
                    echo -e "${RED}Invalid option${NC}"
                    ;;
            esac
            ;;
        5)
            echo -e "${CYAN}Enter custom performance test command: ${NC}"
            read -r custom_perf_command
            eval "$custom_perf_command"
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

# Category 6: Azure Operations
azure_menu() {
    while true; do
        print_category_header "AZURE OPERATIONS"
        echo -e "${GREEN}Available Azure tasks:${NC}"
        echo ""
        echo -e "${YELLOW}1.${NC} Azure Login & Account Info"
        echo -e "${YELLOW}2.${NC} Key Vault Operations"
        echo -e "${YELLOW}3.${NC} Resource Group Management"
        echo -e "${YELLOW}4.${NC} Virtual Machine Operations"
        echo -e "${YELLOW}5.${NC} Storage Account Operations"
        echo -e "${YELLOW}6.${NC} App Service Operations"
        echo -e "${YELLOW}7.${NC} Container Registry Operations"
        echo -e "${YELLOW}8.${NC} Kubernetes Service (AKS) Operations"
        echo -e "${YELLOW}9.${NC} Azure DevOps Operations"
        echo -e "${YELLOW}0.${NC} Back to Main Menu"
        echo ""
        echo -e "${CYAN}Enter your choice [0-9]: ${NC}"

        read -r choice
        case $choice in
            1) azure_login_info ;;
            2) azure_keyvault_operations ;;
            3) azure_resource_group_operations ;;
            4) azure_vm_operations ;;
            5) azure_storage_operations ;;
            6) azure_app_service_operations ;;
            7) azure_container_registry_operations ;;
            8) azure_aks_operations ;;
            9) azure_devops_operations ;;
            0) break ;;
            *) echo -e "${RED}Invalid option. Please try again.${NC}"; press_enter ;;
        esac
    done
}

azure_login_info() {
    echo -e "${GREEN}Azure Login & Account Information${NC}"

    echo -e "${BLUE}Checking Azure CLI installation...${NC}"
    if ! command -v az &> /dev/null; then
        echo -e "${RED}✗ Azure CLI is not installed${NC}"
        echo -e "${CYAN}Install Azure CLI: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli${NC}"
        press_enter
        return
    fi

    echo -e "${GREEN}✓ Azure CLI is installed${NC}"
    az --version | head -1

    echo -e "${BLUE}Current login status:${NC}"
    if az account show &>/dev/null; then
        echo -e "${GREEN}✓ Already logged in${NC}"
        az account show --output table

        echo -e "${CYAN}List all subscriptions? (y/N): ${NC}"
        read -r list_subs
        if [[ "$list_subs" =~ ^[yY]$ ]]; then
            az account list --output table
        fi
    else
        echo -e "${YELLOW}Not logged in to Azure${NC}"
        echo -e "${CYAN}Login now? (y/N): ${NC}"
        read -r do_login
        if [[ "$do_login" =~ ^[yY]$ ]]; then
            az login
        fi
    fi

    press_enter
}

azure_keyvault_operations() {
    echo -e "${GREEN}Azure Key Vault Operations${NC}"

    echo -e "${CYAN}Enter Key Vault name: ${NC}"
    read -r keyvault_name

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. List all secrets"
    echo "2. Get secret value"
    echo "3. Set secret"
    echo "4. Delete secret"
    echo "5. List all keys"
    echo "6. Key Vault information"
    read -r kv_operation

    case $kv_operation in
        1)
            echo -e "${BLUE}Listing all secrets in $keyvault_name...${NC}"
            az keyvault secret list --vault-name "$keyvault_name" --output table
            ;;
        2)
            echo -e "${CYAN}Enter secret name: ${NC}"
            read -r secret_name
            echo -e "${BLUE}Getting secret value...${NC}"
            az keyvault secret show --vault-name "$keyvault_name" --name "$secret_name" --query "value" --output tsv
            ;;
        3)
            echo -e "${CYAN}Enter secret name: ${NC}"
            read -r secret_name
            echo -e "${CYAN}Enter secret value: ${NC}"
            read -s secret_value
            echo

            if confirm_action; then
                log "Setting secret $secret_name in Key Vault $keyvault_name"
                az keyvault secret set --vault-name "$keyvault_name" --name "$secret_name" --value "$secret_value" && \
                echo -e "${GREEN}✓ Secret set successfully${NC}" || \
                echo -e "${RED}✗ Failed to set secret${NC}"
            fi
            ;;
        4)
            echo -e "${CYAN}Enter secret name to delete: ${NC}"
            read -r secret_name
            echo -e "${YELLOW}⚠️  WARNING: This will permanently delete the secret${NC}"

            if confirm_action; then
                log "Deleting secret $secret_name from Key Vault $keyvault_name"
                az keyvault secret delete --vault-name "$keyvault_name" --name "$secret_name" && \
                echo -e "${GREEN}✓ Secret deleted successfully${NC}" || \
                echo -e "${RED}✗ Failed to delete secret${NC}"
            fi
            ;;
        5)
            echo -e "${BLUE}Listing all keys in $keyvault_name...${NC}"
            az keyvault key list --vault-name "$keyvault_name" --output table
            ;;
        6)
            echo -e "${BLUE}Key Vault information for $keyvault_name...${NC}"
            az keyvault show --name "$keyvault_name" --output table
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

azure_resource_group_operations() {
    echo -e "${GREEN}Azure Resource Group Management${NC}"

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. List all resource groups"
    echo "2. Create resource group"
    echo "3. Delete resource group"
    echo "4. Show resource group details"
    echo "5. List resources in group"
    read -r rg_operation

    case $rg_operation in
        1)
            echo -e "${BLUE}Listing all resource groups...${NC}"
            az group list --output table
            ;;
        2)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter location (e.g., eastus, westeurope): ${NC}"
            read -r location

            if confirm_action; then
                log "Creating resource group $rg_name in $location"
                az group create --name "$rg_name" --location "$location" && \
                echo -e "${GREEN}✓ Resource group created successfully${NC}" || \
                echo -e "${RED}✗ Failed to create resource group${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter resource group name to delete: ${NC}"
            read -r rg_name
            echo -e "${YELLOW}⚠️  WARNING: This will delete ALL resources in the group${NC}"

            if confirm_action; then
                log "Deleting resource group $rg_name"
                az group delete --name "$rg_name" --yes && \
                echo -e "${GREEN}✓ Resource group deleted successfully${NC}" || \
                echo -e "${RED}✗ Failed to delete resource group${NC}"
            fi
            ;;
        4)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${BLUE}Resource group details for $rg_name...${NC}"
            az group show --name "$rg_name" --output table
            ;;
        5)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${BLUE}Resources in $rg_name...${NC}"
            az resource list --resource-group "$rg_name" --output table
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

azure_vm_operations() {
    echo -e "${GREEN}Azure Virtual Machine Operations${NC}"

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. List all VMs"
    echo "2. Start VM"
    echo "3. Stop VM"
    echo "4. Restart VM"
    echo "5. Get VM status"
    echo "6. Create VM"
    echo "7. Delete VM"
    read -r vm_operation

    case $vm_operation in
        1)
            echo -e "${BLUE}Listing all virtual machines...${NC}"
            az vm list --output table
            ;;
        2)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter VM name: ${NC}"
            read -r vm_name

            if confirm_action; then
                log "Starting VM $vm_name in resource group $rg_name"
                az vm start --resource-group "$rg_name" --name "$vm_name" && \
                echo -e "${GREEN}✓ VM started successfully${NC}" || \
                echo -e "${RED}✗ Failed to start VM${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter VM name: ${NC}"
            read -r vm_name

            if confirm_action; then
                log "Stopping VM $vm_name in resource group $rg_name"
                az vm stop --resource-group "$rg_name" --name "$vm_name" && \
                echo -e "${GREEN}✓ VM stopped successfully${NC}" || \
                echo -e "${RED}✗ Failed to stop VM${NC}"
            fi
            ;;
        4)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter VM name: ${NC}"
            read -r vm_name

            if confirm_action; then
                log "Restarting VM $vm_name in resource group $rg_name"
                az vm restart --resource-group "$rg_name" --name "$vm_name" && \
                echo -e "${GREEN}✓ VM restarted successfully${NC}" || \
                echo -e "${RED}✗ Failed to restart VM${NC}"
            fi
            ;;
        5)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter VM name: ${NC}"
            read -r vm_name
            echo -e "${BLUE}VM status for $vm_name...${NC}"
            az vm get-instance-view --resource-group "$rg_name" --name "$vm_name" --output table
            ;;
        6)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter VM name: ${NC}"
            read -r vm_name
            echo -e "${CYAN}Enter VM size (e.g., Standard_B1s): ${NC}"
            read -r vm_size
            echo -e "${CYAN}Enter admin username: ${NC}"
            read -r admin_username

            if confirm_action; then
                log "Creating VM $vm_name in resource group $rg_name"
                az vm create \
                    --resource-group "$rg_name" \
                    --name "$vm_name" \
                    --size "$vm_size" \
                    --admin-username "$admin_username" \
                    --generate-ssh-keys \
                    --output table && \
                echo -e "${GREEN}✓ VM created successfully${NC}" || \
                echo -e "${RED}✗ Failed to create VM${NC}"
            fi
            ;;
        7)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter VM name to delete: ${NC}"
            read -r vm_name
            echo -e "${YELLOW}⚠️  WARNING: This will permanently delete the VM${NC}"

            if confirm_action; then
                log "Deleting VM $vm_name from resource group $rg_name"
                az vm delete --resource-group "$rg_name" --name "$vm_name" --yes && \
                echo -e "${GREEN}✓ VM deleted successfully${NC}" || \
                echo -e "${RED}✗ Failed to delete VM${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

azure_storage_operations() {
    echo -e "${GREEN}Azure Storage Account Operations${NC}"

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. List storage accounts"
    echo "2. Create storage account"
    echo "3. Get storage account keys"
    echo "4. List containers"
    echo "5. Upload blob"
    echo "6. Download blob"
    read -r storage_operation

    case $storage_operation in
        1)
            echo -e "${BLUE}Listing all storage accounts...${NC}"
            az storage account list --output table
            ;;
        2)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter storage account name: ${NC}"
            read -r storage_name
            echo -e "${CYAN}Enter location: ${NC}"
            read -r location

            if confirm_action; then
                log "Creating storage account $storage_name"
                az storage account create \
                    --name "$storage_name" \
                    --resource-group "$rg_name" \
                    --location "$location" \
                    --sku Standard_LRS && \
                echo -e "${GREEN}✓ Storage account created successfully${NC}" || \
                echo -e "${RED}✗ Failed to create storage account${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter storage account name: ${NC}"
            read -r storage_name
            echo -e "${BLUE}Getting storage account keys...${NC}"
            az storage account keys list --resource-group "$rg_name" --account-name "$storage_name" --output table
            ;;
        4)
            echo -e "${CYAN}Enter storage account name: ${NC}"
            read -r storage_name
            echo -e "${BLUE}Listing containers...${NC}"
            az storage container list --account-name "$storage_name" --output table
            ;;
        5)
            echo -e "${CYAN}Enter storage account name: ${NC}"
            read -r storage_name
            echo -e "${CYAN}Enter container name: ${NC}"
            read -r container_name
            echo -e "${CYAN}Enter local file path: ${NC}"
            read -r local_file
            echo -e "${CYAN}Enter blob name: ${NC}"
            read -r blob_name

            if [[ ! -f "$local_file" ]]; then
                echo -e "${RED}✗ Local file not found${NC}"
                press_enter
                return
            fi

            if confirm_action; then
                log "Uploading $local_file to blob $blob_name"
                az storage blob upload \
                    --account-name "$storage_name" \
                    --container-name "$container_name" \
                    --name "$blob_name" \
                    --file "$local_file" && \
                echo -e "${GREEN}✓ Blob uploaded successfully${NC}" || \
                echo -e "${RED}✗ Failed to upload blob${NC}"
            fi
            ;;
        6)
            echo -e "${CYAN}Enter storage account name: ${NC}"
            read -r storage_name
            echo -e "${CYAN}Enter container name: ${NC}"
            read -r container_name
            echo -e "${CYAN}Enter blob name: ${NC}"
            read -r blob_name
            echo -e "${CYAN}Enter local file path to save: ${NC}"
            read -r local_file

            if confirm_action; then
                log "Downloading blob $blob_name to $local_file"
                az storage blob download \
                    --account-name "$storage_name" \
                    --container-name "$container_name" \
                    --name "$blob_name" \
                    --file "$local_file" && \
                echo -e "${GREEN}✓ Blob downloaded successfully${NC}" || \
                echo -e "${RED}✗ Failed to download blob${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

azure_app_service_operations() {
    echo -e "${GREEN}Azure App Service Operations${NC}"

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. List app services"
    echo "2. Create app service"
    echo "3. Deploy from Git"
    echo "4. Start app service"
    echo "5. Stop app service"
    echo "6. Get app service logs"
    read -r app_operation

    case $app_operation in
        1)
            echo -e "${BLUE}Listing all app services...${NC}"
            az webapp list --output table
            ;;
        2)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter app service name: ${NC}"
            read -r app_name
            echo -e "${CYAN}Enter app service plan name: ${NC}"
            read -r plan_name

            if confirm_action; then
                log "Creating app service $app_name"
                az webapp create \
                    --resource-group "$rg_name" \
                    --plan "$plan_name" \
                    --name "$app_name" && \
                echo -e "${GREEN}✓ App service created successfully${NC}" || \
                echo -e "${RED}✗ Failed to create app service${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter app service name: ${NC}"
            read -r app_name
            echo -e "${CYAN}Enter Git repository URL: ${NC}"
            read -r git_url

            if confirm_action; then
                log "Deploying $git_url to app service $app_name"
                az webapp deployment source config \
                    --resource-group "$rg_name" \
                    --name "$app_name" \
                    --repo-url "$git_url" \
                    --branch master \
                    --manual-integration && \
                echo -e "${GREEN}✓ Deployment configured successfully${NC}" || \
                echo -e "${RED}✗ Failed to configure deployment${NC}"
            fi
            ;;
        4)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter app service name: ${NC}"
            read -r app_name

            if confirm_action; then
                log "Starting app service $app_name"
                az webapp start --resource-group "$rg_name" --name "$app_name" && \
                echo -e "${GREEN}✓ App service started successfully${NC}" || \
                echo -e "${RED}✗ Failed to start app service${NC}"
            fi
            ;;
        5)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter app service name: ${NC}"
            read -r app_name

            if confirm_action; then
                log "Stopping app service $app_name"
                az webapp stop --resource-group "$rg_name" --name "$app_name" && \
                echo -e "${GREEN}✓ App service stopped successfully${NC}" || \
                echo -e "${RED}✗ Failed to stop app service${NC}"
            fi
            ;;
        6)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter app service name: ${NC}"
            read -r app_name
            echo -e "${BLUE}Getting app service logs...${NC}"
            az webapp log tail --resource-group "$rg_name" --name "$app_name"
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

azure_container_registry_operations() {
    echo -e "${GREEN}Azure Container Registry Operations${NC}"

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. List container registries"
    echo "2. Create container registry"
    echo "3. Login to registry"
    echo "4. List repositories"
    echo "5. Build and push image"
    read -r acr_operation

    case $acr_operation in
        1)
            echo -e "${BLUE}Listing all container registries...${NC}"
            az acr list --output table
            ;;
        2)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter registry name: ${NC}"
            read -r registry_name
            echo -e "${CYAN}Enter SKU (Basic/Standard/Premium): ${NC}"
            read -r sku

            if confirm_action; then
                log "Creating container registry $registry_name"
                az acr create \
                    --resource-group "$rg_name" \
                    --name "$registry_name" \
                    --sku "$sku" && \
                echo -e "${GREEN}✓ Container registry created successfully${NC}" || \
                echo -e "${RED}✗ Failed to create container registry${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter registry name: ${NC}"
            read -r registry_name
            echo -e "${BLUE}Logging in to container registry...${NC}"
            az acr login --name "$registry_name" && \
            echo -e "${GREEN}✓ Logged in successfully${NC}" || \
            echo -e "${RED}✗ Failed to login${NC}"
            ;;
        4)
            echo -e "${CYAN}Enter registry name: ${NC}"
            read -r registry_name
            echo -e "${BLUE}Listing repositories...${NC}"
            az acr repository list --name "$registry_name" --output table
            ;;
        5)
            echo -e "${CYAN}Enter registry name: ${NC}"
            read -r registry_name
            echo -e "${CYAN}Enter image name: ${NC}"
            read -r image_name
            echo -e "${CYAN}Enter Dockerfile path (default: .): ${NC}"
            read -r dockerfile_path
            dockerfile_path=${dockerfile_path:-.}

            if confirm_action; then
                log "Building and pushing image $image_name to registry $registry_name"
                az acr build \
                    --registry "$registry_name" \
                    --image "$image_name" \
                    "$dockerfile_path" && \
                echo -e "${GREEN}✓ Image built and pushed successfully${NC}" || \
                echo -e "${RED}✗ Failed to build and push image${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

azure_aks_operations() {
    echo -e "${GREEN}Azure Kubernetes Service (AKS) Operations${NC}"

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. List AKS clusters"
    echo "2. Create AKS cluster"
    echo "3. Get AKS credentials"
    echo "4. Scale AKS cluster"
    echo "5. Start AKS cluster"
    echo "6. Stop AKS cluster"
    read -r aks_operation

    case $aks_operation in
        1)
            echo -e "${BLUE}Listing all AKS clusters...${NC}"
            az aks list --output table
            ;;
        2)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter cluster name: ${NC}"
            read -r cluster_name
            echo -e "${CYAN}Enter node count (default: 3): ${NC}"
            read -r node_count
            node_count=${node_count:-3}

            if confirm_action; then
                log "Creating AKS cluster $cluster_name"
                az aks create \
                    --resource-group "$rg_name" \
                    --name "$cluster_name" \
                    --node-count "$node_count" \
                    --enable-addons monitoring \
                    --generate-ssh-keys && \
                echo -e "${GREEN}✓ AKS cluster created successfully${NC}" || \
                echo -e "${RED}✗ Failed to create AKS cluster${NC}"
            fi
            ;;
        3)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter cluster name: ${NC}"
            read -r cluster_name
            echo -e "${BLUE}Getting AKS credentials...${NC}"
            az aks get-credentials --resource-group "$rg_name" --name "$cluster_name" && \
            echo -e "${GREEN}✓ Credentials retrieved successfully${NC}" || \
            echo -e "${RED}✗ Failed to get credentials${NC}"
            ;;
        4)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter cluster name: ${NC}"
            read -r cluster_name
            echo -e "${CYAN}Enter new node count: ${NC}"
            read -r node_count

            if confirm_action; then
                log "Scaling AKS cluster $cluster_name to $node_count nodes"
                az aks scale \
                    --resource-group "$rg_name" \
                    --name "$cluster_name" \
                    --node-count "$node_count" && \
                echo -e "${GREEN}✓ AKS cluster scaled successfully${NC}" || \
                echo -e "${RED}✗ Failed to scale AKS cluster${NC}"
            fi
            ;;
        5)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter cluster name: ${NC}"
            read -r cluster_name

            if confirm_action; then
                log "Starting AKS cluster $cluster_name"
                az aks start --resource-group "$rg_name" --name "$cluster_name" && \
                echo -e "${GREEN}✓ AKS cluster started successfully${NC}" || \
                echo -e "${RED}✗ Failed to start AKS cluster${NC}"
            fi
            ;;
        6)
            echo -e "${CYAN}Enter resource group name: ${NC}"
            read -r rg_name
            echo -e "${CYAN}Enter cluster name: ${NC}"
            read -r cluster_name

            if confirm_action; then
                log "Stopping AKS cluster $cluster_name"
                az aks stop --resource-group "$rg_name" --name "$cluster_name" && \
                echo -e "${GREEN}✓ AKS cluster stopped successfully${NC}" || \
                echo -e "${RED}✗ Failed to stop AKS cluster${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

azure_devops_operations() {
    echo -e "${GREEN}Azure DevOps Operations${NC}"

    echo -e "${CYAN}Select operation:${NC}"
    echo "1. List DevOps organizations"
    echo "2. List projects"
    echo "3. List pipelines"
    echo "4. Run pipeline"
    echo "5. List repositories"
    echo "6. Clone repository"
    read -r devops_operation

    case $devops_operation in
        1)
            echo -e "${BLUE}Listing DevOps organizations...${NC}"
            az devops project list --output table
            ;;
        2)
            echo -e "${CYAN}Enter organization URL: ${NC}"
            read -r org_url
            echo -e "${BLUE}Listing projects...${NC}"
            az devops project list --organization "$org_url" --output table
            ;;
        3)
            echo -e "${CYAN}Enter organization URL: ${NC}"
            read -r org_url
            echo -e "${CYAN}Enter project name: ${NC}"
            read -r project_name
            echo -e "${BLUE}Listing pipelines...${NC}"
            az pipelines list --organization "$org_url" --project "$project_name" --output table
            ;;
        4)
            echo -e "${CYAN}Enter organization URL: ${NC}"
            read -r org_url
            echo -e "${CYAN}Enter project name: ${NC}"
            read -r project_name
            echo -e "${CYAN}Enter pipeline ID: ${NC}"
            read -r pipeline_id

            if confirm_action; then
                log "Running pipeline $pipeline_id"
                az pipelines run --organization "$org_url" --project "$project_name" --id "$pipeline_id" && \
                echo -e "${GREEN}✓ Pipeline started successfully${NC}" || \
                echo -e "${RED}✗ Failed to start pipeline${NC}"
            fi
            ;;
        5)
            echo -e "${CYAN}Enter organization URL: ${NC}"
            read -r org_url
            echo -e "${CYAN}Enter project name: ${NC}"
            read -r project_name
            echo -e "${BLUE}Listing repositories...${NC}"
            az repos list --organization "$org_url" --project "$project_name" --output table
            ;;
        6)
            echo -e "${CYAN}Enter repository URL: ${NC}"
            read -r repo_url
            echo -e "${CYAN}Enter local directory name: ${NC}"
            read -r local_dir

            if confirm_action; then
                log "Cloning repository $repo_url to $local_dir"
                git clone "$repo_url" "$local_dir" && \
                echo -e "${GREEN}✓ Repository cloned successfully${NC}" || \
                echo -e "${RED}✗ Failed to clone repository${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    press_enter
}

# View logs function
view_logs() {
    print_category_header "TASK RUNNER LOGS"

    if [[ ! -f "$LOG_FILE" ]]; then
        echo -e "${YELLOW}No log file found. Logs will be created when tasks are executed.${NC}"
        press_enter
        return
    fi

    echo -e "${GREEN}Recent log entries:${NC}"
    echo ""
    tail -50 "$LOG_FILE"
    echo ""
    echo -e "${CYAN}Show full log? (y/N): ${NC}"
    read -r show_full

    if [[ "$show_full" =~ ^[yY]$ ]]; then
        less "$LOG_FILE"
    fi

    press_enter
}

# Main execution loop
main() {
    # Create log file if it doesn't exist
    touch "$LOG_FILE"
    log "Task Runner started"

    while true; do
        show_main_menu
        read -r choice

        case $choice in
            1) build_menu ;;
            2) kubernetes_menu ;;
            3) database_menu ;;
            4) monitoring_menu ;;
            5) testing_menu ;;
            6) azure_menu ;;
            7) view_logs ;;
            0)
                echo -e "${GREEN}Thank you for using DOCMS Task Runner!${NC}"
                log "Task Runner exited"
                exit 0
                ;;
            *)
                echo -e "${RED}Invalid option. Please try again.${NC}"
                press_enter
                ;;
        esac
    done
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

clean_artifacts() {
    echo -e "${GREEN}Clean Build Artifacts${NC}"
    echo -e "${YELLOW}This will remove:${NC}"
    echo "- target/ directories"
    echo "- build/ directories" 
    echo "- node_modules/ directories"
    echo "- *.log files"
    
    if confirm_action; then
        log "Cleaning build artifacts"
        find . -name "target" -type d -exec rm -rf {} + 2>/dev/null || true
        find . -name "build" -type d -exec rm -rf {} + 2>/dev/null || true
        find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
        find . -name "*.log" -type f -delete 2>/dev/null || true
        echo -e "${GREEN}✓ Build artifacts cleaned${NC}"
    fi
    press_enter
}

build_status() {
    echo -e "${GREEN}Build Status Check${NC}"
    echo -e "${BLUE}Checking build environment...${NC}"
    
    echo -e "${CYAN}Docker:${NC}"
    docker --version 2>/dev/null && echo -e "${GREEN}✓ Available${NC}" || echo -e "${RED}✗ Not available${NC}"
    
    echo -e "${CYAN}Maven:${NC}"
    mvn --version 2>/dev/null | head -1 && echo -e "${GREEN}✓ Available${NC}" || echo -e "${RED}✗ Not available${NC}"
    
    echo -e "${CYAN}Node.js:${NC}"
    node --version 2>/dev/null && echo -e "${GREEN}✓ Available${NC}" || echo -e "${RED}✗ Not available${NC}"
    
    echo -e "${CYAN}Gradle:${NC}"
    gradle --version 2>/dev/null | head -1 && echo -e "${GREEN}✓ Available${NC}" || echo -e "${RED}✗ Not available${NC}"
    
    press_enter
}
