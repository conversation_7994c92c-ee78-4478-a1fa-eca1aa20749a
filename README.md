# DOCMS Task Runner

An interactive bash script with **modular architecture** that provides organized categories for common DevOps tasks. This script helps execute repetitive tasks such as common operations on Azure cloud, Kubernetes, databases, and more.

## 🏗️ Modular Architecture

The task runner has been completely refactored into a modular structure for better maintainability:

- **Main Script**: `task-runner-modular.sh` - The orchestrator that loads and manages modules
- **Core Utilities**: `modules/utils.sh` - Shared functions, colors, logging, and utilities
- **Category Modules**: Each category is now a separate module file:
  - `modules/build.sh` - Build operations
  - `modules/kubernetes.sh` - Kubernetes operations
  - `modules/database.sh` - Database operations
  - `modules/monitoring.sh` - Monitoring operations
  - `modules/testing.sh` - Testing operations
  - `modules/azure.sh` - Azure operations

### Benefits of Modular Design:
- ✅ **Better Maintainability** - Each module can be updated independently
- ✅ **Easier Testing** - Individual modules can be tested in isolation
- ✅ **Cleaner Code** - Logical separation of concerns
- ✅ **Extensibility** - New modules can be added easily
- ✅ **Selective Loading** - Only load modules you need

## Features

The script is organized into 6 main categories:

### 1. 🔨 Build Operations
- Docker Build & Push
- Maven Build (clean, compile, package, install, test)
- NPM Build & Package
- Gradle Build
- Clean Build Artifacts
- Build Status Check

### 2. ☸️ Kubernetes Operations
- Get Cluster Info
- Deploy Applications
- Scale Deployments
- View Pods Status
- View Logs
- Port Forward
- Apply Manifests
- Delete Resources

### 3. 🗄️ Database Operations
- PostgreSQL Operations
- MySQL Operations
- MongoDB Operations
- Database Backup & Restore
- Connection Testing
- Schema Migration

### 4. 📊 Monitoring Operations
- System Health Check
- Docker Container Monitoring
- Log Analysis
- Network Monitoring
- Disk Usage Check
- Process Monitoring
- Service Status Check
- Performance Metrics

### 5. 🧪 Testing Operations
- Unit Tests (Maven, NPM/Jest, pytest, Go, Rust)
- Integration Tests
- Load Testing (Apache Bench, curl, JMeter, Artillery)
- API Testing
- Security Testing
- Code Quality Check
- Test Coverage Reports
- Performance Testing

### 6. ☁️ Azure Operations
- Azure Login & Account Info
- Key Vault Operations (list secrets, get/set/delete secrets)
- Resource Group Management
- Virtual Machine Operations
- Storage Account Operations
- App Service Operations
- Container Registry Operations
- Kubernetes Service (AKS) Operations
- Azure DevOps Operations

## Usage

### Prerequisites

Make sure you have the following tools installed based on what operations you plan to use:

**General:**
- `bash` (version 4.0+)
- `curl`
- `git`

**Build Tools:**
- `docker` (for Docker operations)
- `mvn` (for Maven operations)
- `npm` (for NPM operations)
- `gradle` (for Gradle operations)

**Kubernetes:**
- `kubectl` (for Kubernetes operations)

**Databases:**
- `psql` (for PostgreSQL operations)
- `mysql` (for MySQL operations)
- `mongosh` (for MongoDB operations)

**Azure:**
- `az` (Azure CLI for Azure operations)

**Testing:**
- Various testing frameworks based on your needs

### Running the Script

#### Option 1: Modular Version (Recommended)

1. Make the script executable (if not already):
   ```bash
   chmod +x task-runner-modular.sh
   ```

2. Run the modular script:
   ```bash
   ./task-runner-modular.sh
   ```

3. Navigate through the interactive menu using the number keys.

#### Option 2: Legacy Monolithic Version

The original single-file version is still available as `task-runner.sh` for backward compatibility:

```bash
chmod +x task-runner.sh
./task-runner.sh
```

#### Command Line Options

The modular script supports several command-line options:

```bash
./task-runner-modular.sh --help     # Show help
./task-runner-modular.sh --version  # Show version info
./task-runner-modular.sh --status   # Show module status
```

### Example Usage

1. **Azure Key Vault Operations:**
   - Select option `6` (Azure Operations)
   - Select option `2` (Key Vault Operations)
   - Choose your desired operation (list secrets, get secret, etc.)

2. **Docker Build and Push:**
   - Select option `1` (Build Operations)
   - Select option `1` (Docker Build & Push)
   - Enter your image name and tag

3. **Kubernetes Pod Status:**
   - Select option `2` (Kubernetes Operations)
   - Select option `4` (View Pods Status)
   - Enter namespace or leave empty for all namespaces

4. **Module Status Check:**
   - Select option `8` (Module Status)
   - View which modules are loaded and available

## Extensibility

The modular design makes the script highly extensible:

### Adding New Tasks to Existing Modules

1. **Edit the appropriate module file** (e.g., `modules/build.sh`)
2. **Add your function** following the existing patterns
3. **Update the options array** in the module's menu function
4. **Update the functions array** with your function name
5. **Export your function** at the bottom of the module

### Creating New Modules

1. **Create a new module file** in the `modules/` directory
2. **Follow the module template**:
   ```bash
   #!/bin/bash

   # Your Module Name for DOCMS Task Runner

   # Source utilities
   source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

   # Your module menu function
   your_module_menu() {
       local options=(
           "Task 1"
           "Task 2"
       )

       local functions=(
           "task_1_function"
           "task_2_function"
       )

       while true; do
           show_menu_options "YOUR MODULE NAME" "${options[@]}"
           read -r choice

           if ! handle_menu_choice "$choice" "${#options[@]}" "${functions[@]}"; then
               break
           fi
       done
   }

   # Your task functions
   task_1_function() {
       info_message "Task 1"
       # Your task logic here
       press_enter
   }

   # Export functions
   export -f your_module_menu task_1_function
   ```

3. **Add your module** to the `load_modules()` function in `task-runner-modular.sh`
4. **Add a menu option** in the main script

### Example: Adding a New Task

```bash
my_new_task() {
    info_message "My New Task"

    local user_input
    user_input=$(read_input "Enter some input")

    if validate_not_empty "$user_input" "Input" && confirm_action; then
        execute_with_logging "your_command_here" "Executing my new task with input: $user_input"
    fi
    press_enter
}
```

## Logging

All task executions are logged to `task-runner.log` in the same directory as the script. You can view logs by selecting option `7` from the main menu.

## Safety Features

- **Confirmation prompts** for destructive operations
- **Input validation** for critical parameters
- **Error handling** with clear success/failure messages
- **Logging** of all operations for audit trail
- **Color-coded output** for better readability

## Contributing

To contribute new tasks or improvements:

1. Follow the existing code style and patterns
2. Add appropriate error handling
3. Include confirmation prompts for destructive operations
4. Add logging for audit trail
5. Test your additions thoroughly

## License

This script is provided as-is for DevOps automation purposes.
