# DOCMS Task Runner

An interactive bash script that provides organized categories for common DevOps tasks. This script helps execute repetitive tasks such as common operations on Azure cloud, Kubernetes, databases, and more.

## Features

The script is organized into 6 main categories:

### 1. 🔨 Build Operations
- Docker Build & Push
- Maven Build (clean, compile, package, install, test)
- NPM Build & Package
- Gradle Build
- Clean Build Artifacts
- Build Status Check

### 2. ☸️ Kubernetes Operations
- Get Cluster Info
- Deploy Applications
- Scale Deployments
- View Pods Status
- View Logs
- Port Forward
- Apply Manifests
- Delete Resources

### 3. 🗄️ Database Operations
- PostgreSQL Operations
- MySQL Operations
- MongoDB Operations
- Database Backup & Restore
- Connection Testing
- Schema Migration

### 4. 📊 Monitoring Operations
- System Health Check
- Docker Container Monitoring
- Log Analysis
- Network Monitoring
- Disk Usage Check
- Process Monitoring
- Service Status Check
- Performance Metrics

### 5. 🧪 Testing Operations
- Unit Tests (Maven, NPM/Jest, pytest, Go, Rust)
- Integration Tests
- Load Testing (Apache Bench, curl, JMeter, Artillery)
- API Testing
- Security Testing
- Code Quality Check
- Test Coverage Reports
- Performance Testing

### 6. ☁️ Azure Operations
- Azure Login & Account Info
- Key Vault Operations (list secrets, get/set/delete secrets)
- Resource Group Management
- Virtual Machine Operations
- Storage Account Operations
- App Service Operations
- Container Registry Operations
- Kubernetes Service (AKS) Operations
- Azure DevOps Operations

## Usage

### Prerequisites

Make sure you have the following tools installed based on what operations you plan to use:

**General:**
- `bash` (version 4.0+)
- `curl`
- `git`

**Build Tools:**
- `docker` (for Docker operations)
- `mvn` (for Maven operations)
- `npm` (for NPM operations)
- `gradle` (for Gradle operations)

**Kubernetes:**
- `kubectl` (for Kubernetes operations)

**Databases:**
- `psql` (for PostgreSQL operations)
- `mysql` (for MySQL operations)
- `mongosh` (for MongoDB operations)

**Azure:**
- `az` (Azure CLI for Azure operations)

**Testing:**
- Various testing frameworks based on your needs

### Running the Script

1. Make the script executable (if not already):
   ```bash
   chmod +x task-runner.sh
   ```

2. Run the script:
   ```bash
   ./task-runner.sh
   ```

3. Navigate through the interactive menu using the number keys.

### Example Usage

1. **Azure Key Vault Operations:**
   - Select option `6` (Azure Operations)
   - Select option `2` (Key Vault Operations)
   - Choose your desired operation (list secrets, get secret, etc.)

2. **Docker Build and Push:**
   - Select option `1` (Build Operations)
   - Select option `1` (Docker Build & Push)
   - Enter your image name and tag

3. **Kubernetes Pod Status:**
   - Select option `2` (Kubernetes Operations)
   - Select option `4` (View Pods Status)
   - Enter namespace or leave empty for all namespaces

## Extensibility

The script is designed to be easily extensible. To add new tasks:

1. **Add a new function** for your task following the existing pattern
2. **Add the option** to the appropriate category menu
3. **Update the case statement** in the category menu function
4. **Follow the logging pattern** using the `log()` function
5. **Use the confirmation pattern** for destructive operations using `confirm_action()`

### Example: Adding a New Task

```bash
my_new_task() {
    echo -e "${GREEN}My New Task${NC}"
    echo -e "${CYAN}Enter some input: ${NC}"
    read -r user_input
    
    if confirm_action; then
        log "Executing my new task with input: $user_input"
        # Your task logic here
        echo -e "${GREEN}✓ Task completed successfully${NC}" || \
        echo -e "${RED}✗ Task failed${NC}"
    fi
    press_enter
}
```

## Logging

All task executions are logged to `task-runner.log` in the same directory as the script. You can view logs by selecting option `7` from the main menu.

## Safety Features

- **Confirmation prompts** for destructive operations
- **Input validation** for critical parameters
- **Error handling** with clear success/failure messages
- **Logging** of all operations for audit trail
- **Color-coded output** for better readability

## Contributing

To contribute new tasks or improvements:

1. Follow the existing code style and patterns
2. Add appropriate error handling
3. Include confirmation prompts for destructive operations
4. Add logging for audit trail
5. Test your additions thoroughly

## License

This script is provided as-is for DevOps automation purposes.
